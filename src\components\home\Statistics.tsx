"use client";

import { useState, useEffect, useRef } from 'react';
import { motion, useScroll, useTransform } from "framer-motion";
import AchievementsBackground from "./AchievementsBackground";
import CountUp from 'react-countup';
import { useLanguage } from '@/contexts/LanguageContext';

// Custom hook to track element visibility using Intersection Observer
const useIntersectionObserver = (options = {}) => {
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      setIsVisible(entry.isIntersecting);
    }, options);

    const currentRef = ref.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [options]);

  return [ref, isVisible] as [React.RefObject<HTMLDivElement>, boolean];
};

// Achievement Item Component to avoid hooks in map
const AchievementItem = ({ achievement, index, parseValue, iconMapping }: {
  achievement: {
    id: string;
    icon: string;
    value: string;
    label: string;
  };
  index: number;
  parseValue: (value: string) => { prefix: string; numeric: number; suffix: string };
  iconMapping: { [key: string]: JSX.Element };
}) => {
  // Use custom hook for each stat
  const [counterRef, counterVisible] = useIntersectionObserver({
    threshold: 0.3,
    triggerOnce: true
  });

  const parsedValue = parseValue(achievement.value);
  const icon = iconMapping[achievement.icon] || iconMapping.target;

  return (
    <motion.div
      key={achievement.id}
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.6, delay: 0.1 * index + 0.3 }}
      className="group"
    >
      <div className="relative h-full bg-white/10 backdrop-blur-md border border-white/20 rounded-xl p-8 overflow-hidden transition-all duration-500 hover:bg-white/15 hover:border-white/30 hover:translate-y-[-5px]">
        <div className="relative z-10">
          <div className="flex justify-center mb-6 text-[rgb(var(--color-primary))]">
            {icon}
          </div>
          <div ref={counterRef} className="text-5xl font-bold mb-3 text-white text-center">
            {parsedValue.prefix && <span>{parsedValue.prefix}</span>}
            <span style={{ display: 'inline-block' }}>
              {counterVisible ? (
                <CountUp
                  end={parsedValue.numeric}
                  duration={2.5}
                  separator=","
                />
              ) : 0}
            </span>
            {parsedValue.suffix && <span>{parsedValue.suffix}</span>}
          </div>
          <div className="text-white font-medium text-center">{achievement.label}</div>
        </div>

        {/* Subtle background glow */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-purple-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
      </div>
    </motion.div>
  );
};

// Split text animation function
const SplitText = ({ text, className }: { text: string, className?: string }) => {
  return (
    <span className={className}>
      {text.split("").map((char, index) => (
        <motion.span
          key={index}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            duration: 0.6,
            delay: 0.05 * index,
            ease: [0.215, 0.61, 0.355, 1]
          }}
          className="inline-block"
        >
          {char === " " ? "\u00A0" : char}
        </motion.span>
      ))}
    </span>
  );
};

// Icon mapping for achievements
const iconMapping: { [key: string]: JSX.Element } = {
  clock: (
    <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  ),
  home: (
    <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
    </svg>
  ),
  smile: (
    <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  ),
  dollar: (
    <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  ),
  // Add more icons as needed
  target: (
    <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" />
    </svg>
  )
};

// Achievements content interface
interface AchievementsContent {
  id: number;
  badge: string;
  title: string;
  description: string;
  achievements: Array<{
    id: string;
    icon: string;
    value: string;
    label: string;
  }>;
  created_at: string;
  updated_at: string;
}

const Statistics = () => {
  const { locale } = useLanguage();
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLDivElement>(null);

  // API state management - following Hero.tsx pattern
  const [achievementsContent, setAchievementsContent] = useState<AchievementsContent | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Add parallax scrolling effect
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start end", "end start"]
  });

  const contentY = useTransform(scrollYProgress, [0, 1], [50, -50]);
  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0]);

  // Fetch achievements content from API - following Hero.tsx pattern
  useEffect(() => {
    const fetchAchievementsContent = async () => {
      try {
        setIsLoading(true);
        const apiBaseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000';
        const endpoint = `/api/home-page/achievements/${locale}/`;

        console.log('🔄 Fetching achievements content from:', `${apiBaseUrl}${endpoint}`);

        const response = await fetch(`${apiBaseUrl}${endpoint}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        const data = await response.json();
        console.log('📥 Achievements content response:', data);

        if (data.success && data.data) {
          setAchievementsContent(data.data);
        } else {
          console.error('Failed to fetch achievements content:', data.message);
          // Keep achievementsContent as null to fall back to static content
        }
      } catch (error) {
        console.error('Error fetching achievements content:', error);
        // Keep achievementsContent as null to fall back to static content
      } finally {
        setIsLoading(false);
      }
    };

    fetchAchievementsContent();
  }, [locale]);

  // Get content with fallback to static content - following Hero.tsx pattern
  const getContent = () => {
    // Fallback to static content
    const fallbackAchievements = [
      {
        id: "1",
        icon: "clock",
        value: "15",
        label: locale === 'ar' ? "سنة من الخبرة" : "Years of Experience"
      },
      {
        id: "2",
        icon: "home",
        value: "50",
        label: locale === 'ar' ? "مشروع مكتمل" : "Completed Projects"
      },
      {
        id: "3",
        icon: "smile",
        value: "500+",
        label: locale === 'ar' ? "عميل راضي" : "Satisfied Clients"
      },
      {
        id: "4",
        icon: "dollar",
        value: "$2B",
        label: locale === 'ar' ? "قيمة المشاريع" : "Project Value"
      }
    ];

    const fallbackContent = {
      badge: locale === 'ar' ? "سجلنا المثبت" : "Our Track Record",
      title: locale === 'ar' ? "إنجازاتنا" : "Our Achievements",
      description: locale === 'ar'
        ? "مزايا كابيتال أنشأت سجلاً قوياً من التطويرات الناجحة والعملاء الراضين على مر السنين"
        : "Mazaya Capital has established a strong track record of successful developments and satisfied clients over the years",
      achievements: fallbackAchievements
    };

    if (achievementsContent &&
        achievementsContent.achievements &&
        Array.isArray(achievementsContent.achievements) &&
        achievementsContent.achievements.length > 0) {
      return {
        badge: achievementsContent.badge || fallbackContent.badge,
        title: achievementsContent.title || fallbackContent.title,
        description: achievementsContent.description || fallbackContent.description,
        achievements: achievementsContent.achievements
      };
    }

    return fallbackContent;
  };

  const content = getContent();

  // Parse value to extract numeric part, prefix, and suffix
  const parseValue = (value: string) => {
    const match = value.match(/^(\$?)(\d+(?:\.\d+)?)([A-Za-z+%]*)$/);
    if (match) {
      return {
        prefix: match[1] || '',
        numeric: parseFloat(match[2]),
        suffix: match[3] || ''
      };
    }
    return { prefix: '', numeric: 0, suffix: value };
  };

  const containerRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress: containerScrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });

  const containerOpacity = useTransform(containerScrollYProgress, [0, 0.2, 0.8, 1], [0.5, 1, 1, 0.5]);

  const [stars, setStars] = useState<{ id: number; size: number; top: number; left: number; delay: number; duration: number }[]>([]);
  const [isHydrated, setIsHydrated] = useState(false);

  // Generate stars on client-side only
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const newStars = Array.from({ length: 50 }).map((_, index) => ({
        id: index,
        size: Math.random() * 2 + 0.5,
        top: Math.random() * 100,
        left: Math.random() * 100,
        delay: Math.random() * 5,
        duration: Math.floor(Math.random() * 4) + 3
      }));

      setStars(newStars);
      setIsHydrated(true);
    }
  }, []);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, []);

  return (
    <section
      ref={sectionRef}
      className="relative py-28 md:py-32 overflow-hidden bg-[#0A1429]"
    >
      {/* Architectural background */}
      <div className="absolute inset-0 z-0">
        <AchievementsBackground />
      </div>

      {/* Architectural grid overlay */}
      <div className="absolute inset-0 z-10 pointer-events-none">
        <div className="h-full w-full grid grid-cols-6 lg:grid-cols-12">
          {Array.from({ length: 12 }).map((_, i) => (
            <div key={i} className="border-s border-white/5 h-full">
              {i === 0 && <div className="border-e border-white/5 h-full w-full"></div>}
            </div>
          ))}
          {Array.from({ length: 12 }).map((_, i) => (
            <div key={i} className="col-span-full border-t border-white/5 h-0"></div>
          ))}
        </div>
      </div>

      {/* Subtle background patterns */}
      <div className="absolute top-0 start-0 w-full h-full opacity-10 z-5">
        <div className="absolute top-0 end-0 w-[600px] h-[600px] bg-gradient-to-b from-blue-500/30 to-transparent rounded-full filter blur-[120px] -translate-y-1/2 translate-x-1/3"></div>
        <div className="absolute bottom-0 start-0 w-[500px] h-[500px] bg-gradient-to-t from-purple-600/20 to-transparent rounded-full filter blur-[100px] translate-y-1/3 -translate-x-1/4"></div>
      </div>

      {/* Stars effect - client-side only rendering */}
      {isHydrated && (
        <div className="star-field">
          {stars.map((star) => (
            <div
              key={`star-${star.id}`}
              className="star"
              style={{
                width: `${star.size}px`,
                height: `${star.size}px`,
                top: `${star.top}%`,
                left: `${star.left}%`,
                animationDuration: `${star.duration}s`,
                animationDelay: `${star.delay}s`
              }}
            />
          ))}
        </div>
      )}

      <div className="container relative z-20 mx-auto px-4">
        <motion.div style={{ opacity }} className="flex flex-col">
          {/* Section Header */}
          <motion.div
            className="text-center mb-16"
            style={{ y: contentY }}
          >
            {/* Loading state for badge - following Hero.tsx pattern */}
            {isLoading ? (
              <div className="inline-block h-8 w-32 bg-white/20 rounded-full animate-pulse mb-6"></div>
            ) : (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
                className="inline-block px-4 py-1.5 rounded-full bg-white/10 backdrop-blur-md border border-white/20 mb-6"
              >
                <span className="text-sm font-medium tracking-wide text-white">{content.badge}</span>
              </motion.div>
            )}

            {/* Loading state for title - following Hero.tsx pattern */}
            {isLoading ? (
              <div className="h-12 w-80 bg-gradient-to-r from-blue-400/20 to-purple-600/20 rounded mx-auto animate-pulse mb-6"></div>
            ) : (
              <h2 className="text-4xl md:text-5xl font-bold mb-6 leading-tight">
                <SplitText text={content.title} className="block text-white" />
              </h2>
            )}

            {/* Loading state for description - following Hero.tsx pattern */}
            {isLoading ? (
              <div className="max-w-3xl mx-auto space-y-2">
                <div className="h-4 w-full bg-white/20 rounded animate-pulse"></div>
                <div className="h-4 w-3/4 bg-white/20 rounded animate-pulse mx-auto"></div>
              </div>
            ) : (
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.5 }}
                className="text-xl text-gray-300 max-w-3xl mx-auto"
              >
                {content.description}
              </motion.p>
            )}
          </motion.div>

          {/* Loading state for achievements grid */}
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
              {Array.from({ length: 4 }).map((_, index) => (
                <div key={index} className="h-48 bg-white/10 rounded-xl animate-pulse"></div>
              ))}
            </div>
          ) : (
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8"
            >
              {content.achievements && content.achievements.map((achievement, index) => (
                <AchievementItem
                  key={achievement.id}
                  achievement={achievement}
                  index={index}
                  parseValue={parseValue}
                  iconMapping={iconMapping}
                />
              ))}
            </motion.div>
          )}
        </motion.div>
      </div>
    </section>
  );
};

export default Statistics;