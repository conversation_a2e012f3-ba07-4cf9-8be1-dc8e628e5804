"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useLanguage } from '@/contexts/LanguageContext';
import { useAuth } from '@/hooks/useAuth';
import { authenticatedApiCall } from '@/utils/api';
import { FiSave, FiEdit3, FiX, FiEye, FiAward, FiPlus, FiTrash2, FiLoader } from 'react-icons/fi';

// API interfaces matching backend structure
interface AchievementItem {
  id: number;
  icon_name: string;
  achievement_value: string;
  title: {
    en: string;
    ar: string;
  };
  created_at?: string;
  updated_at?: string;
}

interface AchievementsSection {
  id: number;
  badge: {
    en: string;
    ar: string;
  };
  title: {
    en: string;
    ar: string;
  };
  supporting_text: {
    en: string;
    ar: string;
  };
  created_at?: string;
  updated_at?: string;
}

// Icon mapping for achievements - matching the Statistics component
const iconMapping: { [key: string]: JSX.Element } = {
  trophy: (
    <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
    </svg>
  ),
  clock: (
    <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  ),
  home: (
    <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
    </svg>
  ),
  smile: (
    <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  ),
  users: (
    <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
    </svg>
  ),
  star: (
    <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
    </svg>
  ),
  heart: (
    <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
    </svg>
  ),
  chart: (
    <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
    </svg>
  ),
  dollar: (
    <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  ),
  target: (
    <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" />
    </svg>
  )
};

// Icon options for the dropdown
const iconOptions = [
  { value: 'trophy', label: 'Trophy' },
  { value: 'users', label: 'Users' },
  { value: 'star', label: 'Star' },
  { value: 'calendar', label: 'Calendar' },
  { value: 'check-circle', label: 'Check Circle' },
  { value: 'heart', label: 'Heart' },
  { value: 'globe', label: 'Globe' },
  { value: 'shield', label: 'Shield' },
  { value: 'clock', label: 'Clock' },
  { value: 'home', label: 'Home' },
  { value: 'smile', label: 'Smile' },
  { value: 'dollar', label: 'Dollar' },
  { value: 'target', label: 'Target' },
];

// Icon Selector Component
function IconSelector({
  value,
  onChange,
  className = ""
}: {
  value: string;
  onChange: (value: string) => void;
  className?: string;
}) {
  const [isOpen, setIsOpen] = useState(false);
  const selectedIcon = iconOptions.find(option => option.value === value);

  return (
    <div className={`relative ${className}`}>
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] flex items-center justify-between"
      >
        <span>{selectedIcon?.label || 'Select Icon'}</span>
        <FiX className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-45' : 'rotate-0'}`} />
      </button>

      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-gray-600 border border-gray-500 rounded-md shadow-lg max-h-60 overflow-y-auto">
          <div className="grid grid-cols-1 gap-1 p-2">
            {iconOptions.map((option) => (
              <button
                key={option.value}
                type="button"
                onClick={() => {
                  onChange(option.value);
                  setIsOpen(false);
                }}
                className={`flex items-center space-x-3 px-3 py-2 text-left rounded hover:bg-gray-500 transition-colors ${
                  value === option.value ? 'bg-[#00C2FF] text-white' : 'text-gray-300'
                }`}
              >
                <span className="text-sm">{option.label}</span>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

// Achievement Form Component
function AchievementForm({
  achievement,
  onSave,
  onCancel
}: {
  achievement: AchievementItem;
  onSave: (achievement: AchievementItem) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState(achievement);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Icon</label>
          <IconSelector
            value={formData.icon_name}
            onChange={(value) => setFormData(prev => ({ ...prev, icon_name: value }))}
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Value</label>
          <input
            type="text"
            value={formData.achievement_value}
            onChange={(e) => setFormData(prev => ({ ...prev, achievement_value: e.target.value }))}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            placeholder="e.g., 100+, 5 Years, 99%"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Title (English)</label>
          <input
            type="text"
            value={formData.title.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              title: { ...prev.title, en: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
          <input
            type="text"
            value={formData.title.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              title: { ...prev.title, ar: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
        >
          Save Achievement
        </button>
      </div>
    </form>
  );
}

export default function HomePageAchievementsManagement() {
  const router = useRouter();
  const { locale } = useLanguage();
  const { isAuthenticated, tokens, logout } = useAuth();
  const [achievementsSection, setAchievementsSection] = useState<AchievementsSection | null>(null);
  const [achievementItems, setAchievementItems] = useState<AchievementItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [editingSection, setEditingSection] = useState<boolean>(false);
  const [editingItem, setEditingItem] = useState<number | null>(null);

  // Helper function to handle authentication errors (without useCallback to avoid dependency issues)
  const handleAuthError = async (error: any, operation: string) => {
    console.error(`Authentication error during ${operation}:`, error);

    // Check if it's an authentication/authorization error
    const isAuthError = error.message?.includes('Authentication') ||
                       error.message?.includes('Unauthorized') ||
                       error.message?.includes('401') ||
                       error.message?.includes('403') ||
                       error.message?.includes('Token') ||
                       error.message?.includes('expired') ||
                       error.message?.includes('credentials') ||
                       error.message?.includes('permission');

    if (isAuthError) {
      alert('Your session has expired. Please log in again.');

      // Clear authentication and redirect to login
      await logout();
      router.push('/en/login');
      return true;
    }

    return false;
  };

  // Fetch data from API
  useEffect(() => {
    let isMounted = true;
    let timeoutId: NodeJS.Timeout;

    const fetchData = async () => {
      // Only fetch if authenticated
      if (!isAuthenticated || !tokens?.access) {
        if (isMounted) {
          setIsLoading(false);
        }
        return;
      }

      if (isMounted) {
        setIsLoading(true);
      }

      try {
        console.log('🔄 Fetching achievements data...');

        // Use authenticatedApiCall for both endpoints
        const [sectionResponse, itemsResponse] = await Promise.all([
          authenticatedApiCall('/api/admin/home-page/achievements/'),
          authenticatedApiCall('/api/admin/home-page/achievements/items/')
        ]);

        if (!isMounted) return;

        console.log('📊 Section response:', sectionResponse);
        console.log('📊 Items response:', itemsResponse);

        if (sectionResponse.success && sectionResponse.data) {
          setAchievementsSection(sectionResponse.data as AchievementsSection);
        }

        if (itemsResponse.success && itemsResponse.data) {
          // Handle paginated response
          let itemsArray: any[] = [];

          if (Array.isArray(itemsResponse.data)) {
            itemsArray = itemsResponse.data;
          } else if (typeof itemsResponse.data === 'object' && 'items' in itemsResponse.data) {
            const paginatedData = itemsResponse.data as { items: any[] };
            if (Array.isArray(paginatedData.items)) {
              itemsArray = paginatedData.items;
            }
          }

          setAchievementItems(itemsArray);
        }
      } catch (error) {
        if (!isMounted) return;

        console.error('Error fetching achievements data:', error);

        // Check if it's an authentication error
        const isAuthError = await handleAuthError(error, 'fetching data');
        if (!isAuthError) {
          alert('Error loading achievements data. Please check your connection and try again.');
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    // Debounce the fetch to prevent rapid successive calls
    timeoutId = setTimeout(() => {
      fetchData();
    }, 100);

    // Cleanup function
    return () => {
      isMounted = false;
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [locale, isAuthenticated, tokens?.access]); // Include essential auth dependencies but use debouncing

  // Save section data
  const handleSaveSection = async () => {
    if (!achievementsSection) return;

    if (!isAuthenticated || !tokens?.access) {
      alert('You must be logged in to save changes');
      return;
    }

    try {
      setIsSaving(true);

      // Use authenticatedApiCall for proper authentication handling
      const response = await authenticatedApiCall('/api/admin/home-page/achievements/', {
        method: 'PUT',
        body: JSON.stringify({
          badge: achievementsSection.badge,
          title: achievementsSection.title,
          supporting_text: achievementsSection.supporting_text
        })
      });

      if (response.success) {
        alert('Section saved successfully!');
        setEditingSection(false);
        // Update local state with response data
        if (response.data && typeof response.data === 'object') {
          const updatedData = response.data as AchievementsSection;
          setAchievementsSection(prev => prev ? {
            ...prev,
            ...updatedData,
            updated_at: updatedData.updated_at
          } : null);
        }
      } else {
        // Check for authentication errors in response
        const errorMessage = response.message || 'Failed to save section';
        if (errorMessage.includes('Authentication') || errorMessage.includes('Unauthorized') ||
            errorMessage.includes('401') || errorMessage.includes('403')) {
          throw new Error('Authentication expired');
        }
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error('Error saving section:', error);

      // Check if it's an authentication error
      const isAuthError = await handleAuthError(error, 'saving section');
      if (!isAuthError) {
        alert(`Error saving section: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    } finally {
      setIsSaving(false);
    }
  };

  // Add new achievement item
  const addAchievementItem = () => {
    const newItem: AchievementItem = {
      id: Date.now(), // Temporary ID
      icon_name: "target",
      achievement_value: "0",
      title: {
        en: "New Achievement",
        ar: "إنجاز جديد"
      }
    };
    setAchievementItems(prev => [...prev, newItem]);
    setEditingItem(newItem.id);
  };

  // Save achievement item
  const handleSaveItem = async (item: AchievementItem) => {
    if (!isAuthenticated || !tokens?.access) {
      alert('You must be logged in to save changes');
      return;
    }

    try {
      setIsSaving(true);

      // Determine if this is a new item (temporary ID) or existing item
      const isNewItem = item.id > 1000000000; // Temporary IDs are large numbers

      if (isNewItem) {
        // Create new achievement item
        const response = await authenticatedApiCall('/api/admin/home-page/achievements/items/', {
          method: 'POST',
          body: JSON.stringify({
            icon_name: item.icon_name,
            achievement_value: item.achievement_value,
            title: item.title
          })
        });

        if (response.success && response.data) {
          // Replace temporary item with real item from API
          const newItem = response.data as AchievementItem;
          setAchievementItems(prev =>
            prev.map(i => i.id === item.id ? newItem : i)
          );
          alert('Achievement created successfully!');
        } else {
          // Check for authentication errors in response
          const errorMessage = response.message || 'Failed to create achievement';
          if (errorMessage.includes('Authentication') || errorMessage.includes('Unauthorized') ||
              errorMessage.includes('401') || errorMessage.includes('403')) {
            throw new Error('Authentication expired');
          }
          throw new Error(errorMessage);
        }
      } else {
        // Update existing achievement item
        const response = await authenticatedApiCall(`/api/admin/home-page/achievements/items/${item.id}/`, {
          method: 'PUT',
          body: JSON.stringify({
            icon_name: item.icon_name,
            achievement_value: item.achievement_value,
            title: item.title
          })
        });

        if (response.success && response.data) {
          // Update item in local state
          const updatedItem = response.data as AchievementItem;
          setAchievementItems(prev =>
            prev.map(i => i.id === item.id ? updatedItem : i)
          );
          alert('Achievement updated successfully!');
        } else {
          // Check for authentication errors in response
          const errorMessage = response.message || 'Failed to update achievement';
          if (errorMessage.includes('Authentication') || errorMessage.includes('Unauthorized') ||
              errorMessage.includes('401') || errorMessage.includes('403')) {
            throw new Error('Authentication expired');
          }
          throw new Error(errorMessage);
        }
      }

      setEditingItem(null);
    } catch (error) {
      console.error('Error saving item:', error);

      // Check if it's an authentication error
      const isAuthError = await handleAuthError(error, 'saving achievement');
      if (!isAuthError) {
        alert(`Error saving achievement: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    } finally {
      setIsSaving(false);
    }
  };

  // Delete achievement item
  const deleteAchievementItem = async (id: number) => {
    if (!confirm('Are you sure you want to delete this achievement?')) return;

    // Check if this is a temporary item (not yet saved to API)
    const isTemporaryItem = id > 1000000000;

    if (isTemporaryItem) {
      // Just remove from local state since it's not saved to API yet
      setAchievementItems(prev => prev.filter(item => item.id !== id));
      alert('Achievement removed successfully!');
      return;
    }

    if (!isAuthenticated || !tokens?.access) {
      alert('You must be logged in to delete items');
      return;
    }

    try {
      setIsSaving(true);

      // Call admin API to delete the item
      const response = await authenticatedApiCall(`/api/admin/home-page/achievements/items/${id}/`, {
        method: 'DELETE'
      });

      if (response.success) {
        // Remove from local state
        setAchievementItems(prev => prev.filter(item => item.id !== id));
        alert('Achievement deleted successfully!');
      } else {
        // Check for authentication errors in response
        const errorMessage = response.message || 'Failed to delete achievement';
        if (errorMessage.includes('Authentication') || errorMessage.includes('Unauthorized') ||
            errorMessage.includes('401') || errorMessage.includes('403')) {
          throw new Error('Authentication expired');
        }
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error('Error deleting item:', error);

      // Check if it's an authentication error
      const isAuthError = await handleAuthError(error, 'deleting achievement');
      if (!isAuthError) {
        alert(`Error deleting achievement: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    } finally {
      setIsSaving(false);
    }
  };

  // Early authentication check
  if (!isAuthenticated) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <FiLoader className="h-8 w-8 animate-spin text-[#00C2FF] mx-auto mb-4" />
          <p className="text-gray-400">Authentication required. Redirecting to login...</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <FiLoader className="h-8 w-8 animate-spin text-[#00C2FF]" />
        <span className="ml-2 text-white">Loading achievements data...</span>
      </div>
    );
  }

  return (
    <div>
      <div className="pb-5 border-b border-gray-700 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold leading-tight text-white">Home Page - Achievements Section</h1>
          <p className="text-gray-400 mt-1">Manage the achievements section content and statistics</p>
        </div>
        <button
          onClick={handleSaveSection}
          disabled={isSaving}
          className="inline-flex items-center px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors disabled:opacity-50"
        >
          {isSaving ? <FiLoader className="mr-2 h-4 w-4 animate-spin" /> : <FiSave className="mr-2 h-4 w-4" />}
          Save All Changes
        </button>
      </div>

      <div className="mt-6 space-y-8">
        {/* Section Content */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiAward className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Section Content
            </h2>
            <button
              onClick={() => setEditingSection(!editingSection)}
              className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
            >
              {editingSection ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
              {editingSection ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {editingSection && achievementsSection ? (
            <div className="space-y-6">
              {/* Badge */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Badge (English)</label>
                  <input
                    type="text"
                    value={achievementsSection.badge.en}
                    onChange={(e) => setAchievementsSection(prev => prev ? ({
                      ...prev,
                      badge: { ...prev.badge, en: e.target.value }
                    }) : null)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Badge (Arabic)</label>
                  <input
                    type="text"
                    value={achievementsSection.badge.ar}
                    onChange={(e) => setAchievementsSection(prev => prev ? ({
                      ...prev,
                      badge: { ...prev.badge, ar: e.target.value }
                    }) : null)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
              </div>

              {/* Title */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Title (English)</label>
                  <input
                    type="text"
                    value={achievementsSection.title.en}
                    onChange={(e) => setAchievementsSection(prev => prev ? ({
                      ...prev,
                      title: { ...prev.title, en: e.target.value }
                    }) : null)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
                  <input
                    type="text"
                    value={achievementsSection.title.ar}
                    onChange={(e) => setAchievementsSection(prev => prev ? ({
                      ...prev,
                      title: { ...prev.title, ar: e.target.value }
                    }) : null)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
              </div>

              {/* Supporting Text */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Supporting Text (English)</label>
                  <textarea
                    value={achievementsSection.supporting_text.en}
                    onChange={(e) => setAchievementsSection(prev => prev ? ({
                      ...prev,
                      supporting_text: { ...prev.supporting_text, en: e.target.value }
                    }) : null)}
                    rows={4}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Supporting Text (Arabic)</label>
                  <textarea
                    value={achievementsSection.supporting_text.ar}
                    onChange={(e) => setAchievementsSection(prev => prev ? ({
                      ...prev,
                      supporting_text: { ...prev.supporting_text, ar: e.target.value }
                    }) : null)}
                    rows={4}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
              </div>
            </div>
          ) : (
            achievementsSection && (
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-3">Current Badge</label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <span className="text-xs text-gray-500 block mb-1">English</span>
                      <p className="text-white bg-gray-700 p-3 rounded">{achievementsSection.badge.en}</p>
                    </div>
                    <div>
                      <span className="text-xs text-gray-500 block mb-1">Arabic</span>
                      <p className="text-white bg-gray-700 p-3 rounded text-right" dir="rtl">{achievementsSection.badge.ar}</p>
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-3">Current Title</label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <span className="text-xs text-gray-500 block mb-1">English</span>
                      <p className="text-white bg-gray-700 p-3 rounded">{achievementsSection.title.en}</p>
                    </div>
                    <div>
                      <span className="text-xs text-gray-500 block mb-1">Arabic</span>
                      <p className="text-white bg-gray-700 p-3 rounded text-right" dir="rtl">{achievementsSection.title.ar}</p>
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-3">Current Supporting Text</label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <span className="text-xs text-gray-500 block mb-1">English</span>
                      <p className="text-gray-300 bg-gray-700 p-3 rounded text-sm">{achievementsSection.supporting_text.en}</p>
                    </div>
                    <div>
                      <span className="text-xs text-gray-500 block mb-1">Arabic</span>
                      <p className="text-gray-300 bg-gray-700 p-3 rounded text-sm text-right" dir="rtl">{achievementsSection.supporting_text.ar}</p>
                    </div>
                  </div>
                </div>
              </div>
            )
          )}
        </div>

        {/* Achievement Items Section */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiAward className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Achievement Items ({achievementItems.length})
            </h2>
            <button
              onClick={addAchievementItem}
              disabled={isSaving}
              className="inline-flex items-center px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors disabled:opacity-50"
            >
              <FiPlus className="mr-2 h-4 w-4" />
              Add Achievement
            </button>
          </div>

          <div className="space-y-6">
            {achievementItems.map((item, index) => (
              <div key={item.id} className="bg-gray-700 rounded-lg border border-gray-600 p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-white flex items-center">
                    Achievement #{index + 1}
                  </h3>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setEditingItem(editingItem === item.id ? null : item.id)}
                      className="inline-flex items-center px-3 py-1 text-sm bg-gray-600 text-gray-300 rounded hover:bg-gray-500"
                    >
                      {editingItem === item.id ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
                      {editingItem === item.id ? 'Cancel' : 'Edit'}
                    </button>
                    <button
                      onClick={() => deleteAchievementItem(item.id)}
                      className="inline-flex items-center px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-500"
                    >
                      <FiTrash2 className="mr-1 h-4 w-4" />
                      Delete
                    </button>
                  </div>
                </div>

                {editingItem === item.id ? (
                  <AchievementForm
                    achievement={item}
                    onSave={handleSaveItem}
                    onCancel={() => setEditingItem(null)}
                  />
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-1">Icon</label>
                      <span className="text-gray-300 text-sm capitalize">{item.icon_name}</span>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-1">Value</label>
                      <p className="text-white font-bold text-lg">{item.achievement_value}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-1">Title (English)</label>
                      <p className="text-gray-300 text-sm">{item.title.en}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-1">Title (Arabic)</label>
                      <p className="text-gray-300 text-sm text-right" dir="rtl">{item.title.ar}</p>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Preview Section */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <h2 className="text-xl font-semibold text-white flex items-center mb-4">
            <FiEye className="mr-2 h-5 w-5 text-[#00C2FF]" />
            Live Preview
          </h2>
          <div className="bg-gray-900 rounded-lg p-8 border border-gray-600">
            {achievementsSection && (
              <div className="text-center mb-16">
                <div className="inline-block px-4 py-1.5 rounded-full bg-white/10 backdrop-blur-md border border-white/20 mb-6">
                  <span className="text-sm font-medium tracking-wide text-white">{achievementsSection.badge.en}</span>
                </div>
                <h2 className="text-4xl md:text-5xl font-bold mb-6 leading-tight text-white">
                  {achievementsSection.title.en}
                </h2>
                <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                  {achievementsSection.supporting_text.en}
                </p>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
              {achievementItems.map((item) => (
                <div key={item.id} className="group">
                  <div className="relative h-full bg-white/10 backdrop-blur-md border border-white/20 rounded-xl p-8 overflow-hidden transition-all duration-500 hover:bg-white/15 hover:border-white/30">
                    <div className="relative z-10">
                      <div className="flex justify-center mb-6 text-[#00C2FF]">
                        {iconMapping[item.icon_name] || iconMapping.target}
                      </div>
                      <div className="text-5xl font-bold mb-3 text-white text-center">
                        {item.achievement_value}
                      </div>
                      <div className="text-white font-medium text-center">
                        {item.title.en}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
