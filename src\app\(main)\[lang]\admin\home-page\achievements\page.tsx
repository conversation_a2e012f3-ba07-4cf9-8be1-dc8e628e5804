"use client";

import { useState } from 'react';
import {   FiSave, FiEdit3, FiX, FiEye, FiAward, FiPlus, FiTrash2, FiClock, FiHome, FiSmile, FiDollarSign,   FiTarget, FiTrendingUp, FiUsers, FiBarChart, FiStar, FiShield, FiGlobe, FiHeart,   FiCheckCircle, FiPieChart, FiActivity, FiZap, FiCoffee, FiCalendar, FiMapPin, FiTool,   FiBriefcase, FiTrendingDown, FiMaximize, FiLayers, FiDatabase, FiCpu, FiWifi, FiMonitor,  FiKey, FiLock, FiUnlock, FiMail, FiPhone, FiCamera, FiImage, FiFile, FiFolder, FiArchive,  FiBook, FiBookmark, FiFlag, FiGift, FiMusic, FiHeadphones, FiMic, FiVideo, FiSettings,  FiSliders, FiVolume2, FiSun, FiMoon, FiCloud, FiUmbrella,  FiAnchor, FiCompass, FiNavigation, FiNavigation2, FiMap, <PERSON>Truck, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ove} from 'react-icons/fi';

interface AchievementCard {
  id: string;
  icon: string;
  value: string;
  label: {
    en: string;
    ar: string;
  };
}

interface AchievementsContent {
  badge: {
    en: string;
    ar: string;
  };
  title: {
    en: string;
    ar: string;
  };
  description: {
    en: string;
    ar: string;
  };
  achievements: AchievementCard[];
}

const iconOptions = [
  // Time & Progress
  { value: 'clock', label: 'Clock', icon: FiClock },
  { value: 'calendar', label: 'Calendar', icon: FiCalendar },
  { value: 'activity', label: 'Activity', icon: FiActivity },
  { value: 'trending-up', label: 'Trending Up', icon: FiTrendingUp },
  { value: 'trending-down', label: 'Trending Down', icon: FiTrendingDown },
  { value: 'zap', label: 'Lightning', icon: FiZap },
  
  // Buildings & Real Estate
  { value: 'home', label: 'Building', icon: FiHome },
  { value: 'layers', label: 'Layers', icon: FiLayers },
  { value: 'maximize', label: 'Maximize', icon: FiMaximize },
  { value: 'map-pin', label: 'Location', icon: FiMapPin },
  { value: 'map', label: 'Map', icon: FiMap },
  
  // People & Social
  { value: 'users', label: 'Users', icon: FiUsers },
  { value: 'smile', label: 'Smile', icon: FiSmile },
  { value: 'heart', label: 'Heart', icon: FiHeart },
  
  // Financial & Business
  { value: 'dollar', label: 'Dollar', icon: FiDollarSign },
  { value: 'briefcase', label: 'Briefcase', icon: FiBriefcase },
  
  // Achievements & Success
  { value: 'target', label: 'Target', icon: FiTarget },
  { value: 'award', label: 'Award', icon: FiAward },
  { value: 'star', label: 'Star', icon: FiStar },
  { value: 'check-circle', label: 'Check Circle', icon: FiCheckCircle },
  { value: 'flag', label: 'Flag', icon: FiFlag },
  { value: 'gift', label: 'Gift', icon: FiGift },
  
  // Analytics & Data
  { value: 'bar-chart', label: 'Bar Chart', icon: FiBarChart },
  { value: 'pie-chart', label: 'Pie Chart', icon: FiPieChart },
  { value: 'database', label: 'Database', icon: FiDatabase },
  { value: 'cpu', label: 'CPU', icon: FiCpu },
  { value: 'monitor', label: 'Monitor', icon: FiMonitor },
  
  // Security & Trust
  { value: 'shield', label: 'Shield', icon: FiShield },
  { value: 'lock', label: 'Lock', icon: FiLock },
  { value: 'unlock', label: 'Unlock', icon: FiUnlock },
  { value: 'key', label: 'Key', icon: FiKey },
  
  // Communication & Connection
  { value: 'globe', label: 'Globe', icon: FiGlobe },
  { value: 'wifi', label: 'WiFi', icon: FiWifi },
  { value: 'mail', label: 'Mail', icon: FiMail },
  { value: 'phone', label: 'Phone', icon: FiPhone },
  
  // Tools & Services
  { value: 'tool', label: 'Tool', icon: FiTool },
  { value: 'settings', label: 'Settings', icon: FiSettings },
  { value: 'sliders', label: 'Sliders', icon: FiSliders },
  { value: 'coffee', label: 'Coffee', icon: FiCoffee },
  
  // Media & Content
  { value: 'camera', label: 'Camera', icon: FiCamera },
  { value: 'image', label: 'Image', icon: FiImage },
  { value: 'video', label: 'Video', icon: FiVideo },
  { value: 'music', label: 'Music', icon: FiMusic },
  { value: 'headphones', label: 'Headphones', icon: FiHeadphones },
  { value: 'mic', label: 'Microphone', icon: FiMic },
  
  // Files & Storage
  { value: 'file', label: 'File', icon: FiFile },
  { value: 'folder', label: 'Folder', icon: FiFolder },
  { value: 'archive', label: 'Archive', icon: FiArchive },
  { value: 'book', label: 'Book', icon: FiBook },
  { value: 'bookmark', label: 'Bookmark', icon: FiBookmark },
  
  // Navigation & Direction
  { value: 'compass', label: 'Compass', icon: FiCompass },
  { value: 'navigation', label: 'Navigation', icon: FiNavigation },
  { value: 'navigation-2', label: 'Navigation 2', icon: FiNavigation2 },
  { value: 'anchor', label: 'Anchor', icon: FiAnchor },
  
  // Weather & Environment
  { value: 'sun', label: 'Sun', icon: FiSun },
  { value: 'moon', label: 'Moon', icon: FiMoon },
  { value: 'cloud', label: 'Cloud', icon: FiCloud },
  { value: 'umbrella', label: 'Umbrella', icon: FiUmbrella },
  
  // Logistics & Delivery
  { value: 'truck', label: 'Truck', icon: FiTruck },
  { value: 'package', label: 'Package', icon: FiPackage },
  
  // Miscellaneous
  { value: 'volume-2', label: 'Volume', icon: FiVolume2 },
];

// Custom Icon Selector Component
function IconSelector({ 
  value, 
  onChange, 
  className = "" 
}: { 
  value: string;
  onChange: (value: string) => void;
  className?: string;
}) {
  const [isOpen, setIsOpen] = useState(false);
  const selectedIcon = iconOptions.find(option => option.value === value);
  const SelectedIconComponent = selectedIcon?.icon || FiTarget;

  return (
    <div className={`relative ${className}`}>
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] flex items-center justify-between"
      >
        <div className="flex items-center space-x-2">
          <SelectedIconComponent className="h-4 w-4 text-[#00C2FF]" />
          <span>{selectedIcon?.label || 'Select Icon'}</span>
        </div>
        <FiX className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-45' : 'rotate-0'}`} />
      </button>

      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-gray-600 border border-gray-500 rounded-md shadow-lg max-h-60 overflow-y-auto">
          <div className="grid grid-cols-1 gap-1 p-2">
            {iconOptions.map((option) => {
              const IconComponent = option.icon;
              return (
                <button
                  key={option.value}
                  type="button"
                  onClick={() => {
                    onChange(option.value);
                    setIsOpen(false);
                  }}
                  className={`flex items-center space-x-3 px-3 py-2 text-left rounded hover:bg-gray-500 transition-colors ${
                    value === option.value ? 'bg-[#00C2FF] text-white' : 'text-gray-300'
                  }`}
                >
                  <IconComponent className={`h-4 w-4 ${value === option.value ? 'text-white' : 'text-[#00C2FF]'}`} />
                  <span className="text-sm">{option.label}</span>
                </button>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}

// Achievement Form Component
function AchievementForm({ 
  achievement, 
  onSave, 
  onCancel 
}: { 
  achievement: AchievementCard;
  onSave: (achievement: AchievementCard) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState(achievement);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Icon</label>
          <IconSelector
            value={formData.icon}
            onChange={(value) => setFormData(prev => ({ ...prev, icon: value }))}
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Value</label>
          <input
            type="text"
            value={formData.value}
            onChange={(e) => setFormData(prev => ({ ...prev, value: e.target.value }))}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Label (English)</label>
          <input
            type="text"
            value={formData.label.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              label: { ...prev.label, en: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Label (Arabic)</label>
          <input
            type="text"
            value={formData.label.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              label: { ...prev.label, ar: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
        >
          Save Achievement
        </button>
      </div>
    </form>
  );
}

export default function HomePageAchievementsManagement() {
  const [achievementsContent, setAchievementsContent] = useState<AchievementsContent>({
    badge: {
      en: "Our Track Record",
      ar: "سجلنا المثبت"
    },
    title: {
      en: "Our Achievements",
      ar: "إنجازاتنا"
    },
    description: {
      en: "Mazaya Capital has established a strong track record of successful developments and satisfied clients over the years",
      ar: "مزايا كابيتال أنشأت سجلاً قوياً من التطويرات الناجحة والعملاء الراضين على مر السنين"
    },
    achievements: [
      {
        id: "1",
        icon: "clock",
        value: "15",
        label: {
          en: "Years of Experience",
          ar: "سنة من الخبرة"
        }
      },
      {
        id: "2",
        icon: "home",
        value: "50",
        label: {
          en: "Completed Projects",
          ar: "مشروع مكتمل"
        }
      },
      {
        id: "3",
        icon: "smile",
        value: "500+",
        label: {
          en: "Satisfied Clients",
          ar: "عميل راضي"
        }
      },
      {
        id: "4",
        icon: "dollar",
        value: "$2B",
        label: {
          en: "Project Value",
          ar: "قيمة المشاريع"
        }
      }
    ]
  });

  const [editingSection, setEditingSection] = useState<string | null>(null);
  const [editingAchievement, setEditingAchievement] = useState<string | null>(null);
  const [draggedItem, setDraggedItem] = useState<string | null>(null);
  const [dragOverItem, setDragOverItem] = useState<string | null>(null);

  const handleSaveAll = () => {
    console.log('Saving achievements content:', achievementsContent);
    alert('Achievements content saved successfully!');
  };

  const addAchievement = () => {
    if (achievementsContent.achievements.length >= 4) return;
    
    const newAchievement: AchievementCard = {
      id: Date.now().toString(),
      icon: "target",
      value: "0",
      label: {
        en: "New Achievement",
        ar: "إنجاز جديد"
      }
    };
    setAchievementsContent(prev => ({
      ...prev,
      achievements: [...prev.achievements, newAchievement]
    }));
    setEditingAchievement(newAchievement.id);
  };

  const deleteAchievement = (id: string) => {
    setAchievementsContent(prev => ({
      ...prev,
      achievements: prev.achievements.filter(a => a.id !== id)
    }));
  };

  const updateAchievement = (id: string, updatedAchievement: AchievementCard) => {
    setAchievementsContent(prev => ({
      ...prev,
      achievements: prev.achievements.map(a => a.id === id ? updatedAchievement : a)
    }));
  };

  const getIconComponent = (iconName: string) => {
    const iconOption = iconOptions.find(opt => opt.value === iconName);
    return iconOption ? iconOption.icon : FiTarget;
  };

  // Drag and Drop Functions
  const handleDragStart = (e: React.DragEvent, achievementId: string) => {
    setDraggedItem(achievementId);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent, achievementId: string) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setDragOverItem(achievementId);
  };

  const handleDragLeave = () => {
    setDragOverItem(null);
  };

  const handleDrop = (e: React.DragEvent, targetId: string) => {
    e.preventDefault();
    
    if (!draggedItem || draggedItem === targetId) {
      setDraggedItem(null);
      setDragOverItem(null);
      return;
    }

    const achievements = [...achievementsContent.achievements];
    const draggedIndex = achievements.findIndex(item => item.id === draggedItem);
    const targetIndex = achievements.findIndex(item => item.id === targetId);

    if (draggedIndex !== -1 && targetIndex !== -1) {
      // Remove the dragged item
      const [draggedAchievement] = achievements.splice(draggedIndex, 1);
      // Insert it at the target position
      achievements.splice(targetIndex, 0, draggedAchievement);
      
      setAchievementsContent(prev => ({
        ...prev,
        achievements
      }));
    }

    setDraggedItem(null);
    setDragOverItem(null);
  };

  const handleDragEnd = () => {
    setDraggedItem(null);
    setDragOverItem(null);
  };

  return (
    <div>
      <div className="pb-5 border-b border-gray-700 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold leading-tight text-white">Home Page - Achievements Section</h1>
          <p className="text-gray-400 mt-1">Manage the achievements section content, statistics, and performance metrics</p>
        </div>
        <button
          onClick={handleSaveAll}
          className="inline-flex items-center px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
        >
          <FiSave className="mr-2 h-4 w-4" />
          Save All Changes
        </button>
      </div>

      <div className="mt-6 space-y-8">
        {/* Main Content Section */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiAward className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Section Content
            </h2>
            <button
              onClick={() => setEditingSection(editingSection === 'main' ? null : 'main')}
              className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
            >
              {editingSection === 'main' ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
              {editingSection === 'main' ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {editingSection === 'main' ? (
            <div className="space-y-6">
              {/* Badge */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Badge (English)</label>
                  <input
                    type="text"
                    value={achievementsContent.badge.en}
                    onChange={(e) => setAchievementsContent(prev => ({
                      ...prev,
                      badge: { ...prev.badge, en: e.target.value }
                    }))}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Badge (Arabic)</label>
                  <input
                    type="text"
                    value={achievementsContent.badge.ar}
                    onChange={(e) => setAchievementsContent(prev => ({
                      ...prev,
                      badge: { ...prev.badge, ar: e.target.value }
                    }))}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
              </div>

              {/* Title */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Title (English)</label>
                  <input
                    type="text"
                    value={achievementsContent.title.en}
                    onChange={(e) => setAchievementsContent(prev => ({
                      ...prev,
                      title: { ...prev.title, en: e.target.value }
                    }))}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
                  <input
                    type="text"
                    value={achievementsContent.title.ar}
                    onChange={(e) => setAchievementsContent(prev => ({
                      ...prev,
                      title: { ...prev.title, ar: e.target.value }
                    }))}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
              </div>

              {/* Description */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Description (English)</label>
                  <textarea
                    value={achievementsContent.description.en}
                    onChange={(e) => setAchievementsContent(prev => ({
                      ...prev,
                      description: { ...prev.description, en: e.target.value }
                    }))}
                    rows={4}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Description (Arabic)</label>
                  <textarea
                    value={achievementsContent.description.ar}
                    onChange={(e) => setAchievementsContent(prev => ({
                      ...prev,
                      description: { ...prev.description, ar: e.target.value }
                    }))}
                    rows={4}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setEditingSection(null)}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => setEditingSection(null)}
                  className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
                >
                  Save Changes
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Badge Display */}
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-3">Current Badge</label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="text-xs text-gray-500 block mb-1">English</span>
                    <p className="text-white bg-gray-700 p-3 rounded">{achievementsContent.badge.en}</p>
                  </div>
                  <div>
                    <span className="text-xs text-gray-500 block mb-1">Arabic</span>
                    <p className="text-white bg-gray-700 p-3 rounded text-right" dir="rtl">{achievementsContent.badge.ar}</p>
                  </div>
                </div>
              </div>

              {/* Title Display */}
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-3">Current Title</label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="text-xs text-gray-500 block mb-1">English</span>
                    <p className="text-white bg-gray-700 p-3 rounded">{achievementsContent.title.en}</p>
                  </div>
                  <div>
                    <span className="text-xs text-gray-500 block mb-1">Arabic</span>
                    <p className="text-white bg-gray-700 p-3 rounded text-right" dir="rtl">{achievementsContent.title.ar}</p>
                  </div>
                </div>
              </div>

              {/* Description Display */}
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-3">Current Description</label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="text-xs text-gray-500 block mb-1">English</span>
                    <p className="text-gray-300 bg-gray-700 p-3 rounded text-sm">{achievementsContent.description.en}</p>
                  </div>
                  <div>
                    <span className="text-xs text-gray-500 block mb-1">Arabic</span>
                    <p className="text-gray-300 bg-gray-700 p-3 rounded text-sm text-right" dir="rtl">{achievementsContent.description.ar}</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Achievement Cards Section */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiBarChart className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Achievement Cards ({achievementsContent.achievements.length}/4)
            </h2>
            {achievementsContent.achievements.length < 4 ? (
              <button
                onClick={addAchievement}
                className="inline-flex items-center px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
              >
                <FiPlus className="mr-2 h-4 w-4" />
                Add Achievement
              </button>
            ) : (
              <div className="text-sm text-gray-400 bg-gray-700 px-3 py-2 rounded-lg">
                Maximum 4 achievements reached
              </div>
            )}
          </div>
          {achievementsContent.achievements.length > 1 && (
            <div className="mb-4 p-3 bg-blue-900/20 border border-blue-500/30 rounded-lg">
              <p className="text-sm text-blue-300 flex items-center">
                <FiMove className="mr-2 h-4 w-4" />
                Drag and drop the cards to reorder them. Use the handle icon to drag.
              </p>
            </div>
          )}
          <div className="space-y-6">
            {achievementsContent.achievements.map((achievement, index) => {
              const IconComponent = getIconComponent(achievement.icon);
              const isDragging = draggedItem === achievement.id;
              const isDragOver = dragOverItem === achievement.id;
              
              return (
                <div
                  key={achievement.id}
                  draggable={true}
                  onDragStart={(e) => handleDragStart(e, achievement.id)}
                  onDragOver={(e) => handleDragOver(e, achievement.id)}
                  onDragLeave={handleDragLeave}
                  onDrop={(e) => handleDrop(e, achievement.id)}
                  onDragEnd={handleDragEnd}
                  className={`bg-gray-700 rounded-lg border border-gray-600 p-6 transition-all duration-200 cursor-move ${
                    isDragging ? 'opacity-50 scale-105 border-[#00C2FF]' : ''
                  } ${
                    isDragOver ? 'border-[#00C2FF] border-2' : ''
                  }`}
                >
                  <div className="flex justify-between items-center mb-4">
                    <div className="flex items-center">
                      <FiMove className="mr-3 h-4 w-4 text-gray-400 cursor-grab active:cursor-grabbing" title="Drag to reorder" />
                      <h3 className="text-lg font-medium text-white flex items-center">
                        <IconComponent className="mr-2 h-5 w-5 text-[#00C2FF]" />
                        Achievement #{index + 1}
                      </h3>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setEditingAchievement(editingAchievement === achievement.id ? null : achievement.id)}
                        className="inline-flex items-center px-3 py-1 text-sm bg-gray-600 text-gray-300 rounded hover:bg-gray-500"
                      >
                        {editingAchievement === achievement.id ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
                        {editingAchievement === achievement.id ? 'Cancel' : 'Edit'}
                      </button>
                      <button
                        onClick={() => deleteAchievement(achievement.id)}
                        className="inline-flex items-center px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-500"
                      >
                        <FiTrash2 className="mr-1 h-4 w-4" />
                        Delete
                      </button>
                    </div>
                  </div>

                  {editingAchievement === achievement.id ? (
                    <AchievementForm
                      achievement={achievement}
                      onSave={(updatedAchievement) => {
                        updateAchievement(achievement.id, updatedAchievement);
                        setEditingAchievement(null);
                      }}
                      onCancel={() => setEditingAchievement(null)}
                    />
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-400 mb-1">Icon</label>
                        <div className="flex items-center space-x-2">
                          <IconComponent className="h-5 w-5 text-[#00C2FF]" />
                          <span className="text-gray-300 text-sm capitalize">{achievement.icon}</span>
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-400 mb-1">Value</label>
                        <p className="text-white font-bold text-lg">{achievement.value}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-400 mb-1">Label (English)</label>
                        <p className="text-gray-300 text-sm">{achievement.label.en}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-400 mb-1">Label (Arabic)</label>
                        <p className="text-gray-300 text-sm text-right" dir="rtl">{achievement.label.ar}</p>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Preview Section */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <h2 className="text-xl font-semibold text-white flex items-center mb-4">
            <FiEye className="mr-2 h-5 w-5 text-[#00C2FF]" />
            Live Preview
          </h2>
          <div className="bg-gray-900 rounded-lg p-8 border border-gray-600">
            <div className="text-center mb-16">
              <div className="inline-block px-4 py-1.5 rounded-full bg-white/10 backdrop-blur-md border border-white/20 mb-6">
                <span className="text-sm font-medium tracking-wide text-white">{achievementsContent.badge.en}</span>
              </div>
              <h2 className="text-4xl md:text-5xl font-bold mb-6 leading-tight text-white">
                {achievementsContent.title.en}
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                {achievementsContent.description.en}
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
              {achievementsContent.achievements.map((achievement) => {
                const IconComponent = getIconComponent(achievement.icon);
                return (
                  <div key={achievement.id} className="group">
                    <div className="relative h-full bg-white/10 backdrop-blur-md border border-white/20 rounded-xl p-8 overflow-hidden transition-all duration-500 hover:bg-white/15 hover:border-white/30">
                      <div className="relative z-10">
                        <div className="flex justify-center mb-6 text-[#00C2FF]">
                          <IconComponent className="h-8 w-8" />
                        </div>
                        <div className="text-5xl font-bold mb-3 text-white text-center">
                          {achievement.value}
                        </div>
                        <div className="text-white font-medium text-center">
                          {achievement.label.en}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 