"use client";

import { useState, useEffect } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useAuth } from '@/hooks/useAuth';
import { authenticatedApiCall, getAuthHeaders } from '@/utils/api';
import { FiSave, FiEdit3, FiX, FiEye, FiAward, FiPlus, FiTrash2, FiLoader } from 'react-icons/fi';

// API interfaces matching backend structure
interface AchievementItem {
  id: number;
  icon_name: string;
  achievement_value: string;
  title: {
    en: string;
    ar: string;
  };
  created_at?: string;
  updated_at?: string;
}

interface AchievementsSection {
  id: number;
  badge: {
    en: string;
    ar: string;
  };
  title: {
    en: string;
    ar: string;
  };
  supporting_text: {
    en: string;
    ar: string;
  };
  created_at?: string;
  updated_at?: string;
}

// Icon options for the dropdown
const iconOptions = [
  { value: 'trophy', label: 'Trophy' },
  { value: 'users', label: 'Users' },
  { value: 'star', label: 'Star' },
  { value: 'calendar', label: 'Calendar' },
  { value: 'check-circle', label: 'Check Circle' },
  { value: 'heart', label: 'Heart' },
  { value: 'globe', label: 'Globe' },
  { value: 'shield', label: 'Shield' },
  { value: 'clock', label: 'Clock' },
  { value: 'home', label: 'Home' },
  { value: 'smile', label: 'Smile' },
  { value: 'dollar', label: 'Dollar' },
  { value: 'target', label: 'Target' },
];

// Icon Selector Component
function IconSelector({
  value,
  onChange,
  className = ""
}: {
  value: string;
  onChange: (value: string) => void;
  className?: string;
}) {
  const [isOpen, setIsOpen] = useState(false);
  const selectedIcon = iconOptions.find(option => option.value === value);

  return (
    <div className={`relative ${className}`}>
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] flex items-center justify-between"
      >
        <span>{selectedIcon?.label || 'Select Icon'}</span>
        <FiX className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-45' : 'rotate-0'}`} />
      </button>

      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-gray-600 border border-gray-500 rounded-md shadow-lg max-h-60 overflow-y-auto">
          <div className="grid grid-cols-1 gap-1 p-2">
            {iconOptions.map((option) => (
              <button
                key={option.value}
                type="button"
                onClick={() => {
                  onChange(option.value);
                  setIsOpen(false);
                }}
                className={`flex items-center space-x-3 px-3 py-2 text-left rounded hover:bg-gray-500 transition-colors ${
                  value === option.value ? 'bg-[#00C2FF] text-white' : 'text-gray-300'
                }`}
              >
                <span className="text-sm">{option.label}</span>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

// Achievement Form Component
function AchievementForm({
  achievement,
  onSave,
  onCancel
}: {
  achievement: AchievementItem;
  onSave: (achievement: AchievementItem) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState(achievement);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Icon</label>
          <IconSelector
            value={formData.icon_name}
            onChange={(value) => setFormData(prev => ({ ...prev, icon_name: value }))}
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Value</label>
          <input
            type="text"
            value={formData.achievement_value}
            onChange={(e) => setFormData(prev => ({ ...prev, achievement_value: e.target.value }))}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            placeholder="e.g., 100+, 5 Years, 99%"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Title (English)</label>
          <input
            type="text"
            value={formData.title.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              title: { ...prev.title, en: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
          <input
            type="text"
            value={formData.title.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              title: { ...prev.title, ar: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
        >
          Save Achievement
        </button>
      </div>
    </form>
  );
}

export default function HomePageAchievementsManagement() {
  const { locale } = useLanguage();
  const { isAuthenticated, tokens } = useAuth();
  const [achievementsSection, setAchievementsSection] = useState<AchievementsSection | null>(null);
  const [achievementItems, setAchievementItems] = useState<AchievementItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [editingSection, setEditingSection] = useState<boolean>(false);
  const [editingItem, setEditingItem] = useState<number | null>(null);

  // Fetch data from API
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const apiBaseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000';

        // Try admin endpoints first if authenticated, fallback to public endpoints
        const tryAdminEndpoints = async () => {
          if (isAuthenticated && tokens?.access) {
            try {
              console.log('🔐 Using authenticated admin endpoints');
              const authHeaders = getAuthHeaders();

              const [sectionResponse, itemsResponse] = await Promise.all([
                fetch(`${apiBaseUrl}/api/admin/home-page/achievements/`, {
                  headers: {
                    'Content-Type': 'application/json',
                    ...authHeaders,
                  }
                }),
                fetch(`${apiBaseUrl}/api/admin/home-page/achievements/items/`, {
                  headers: {
                    'Content-Type': 'application/json',
                    ...authHeaders,
                  }
                })
              ]);

              if (sectionResponse.ok && itemsResponse.ok) {
                return await Promise.all([
                  sectionResponse.json(),
                  itemsResponse.json()
                ]);
              }

              console.warn('Admin endpoints failed, falling back to public endpoints');
              throw new Error('Admin endpoints not available');
            } catch (error) {
              console.error('Admin API error:', error);
              // Fall through to public endpoints
            }
          }

          // Fallback to public endpoints
          console.log('📖 Using public endpoints');
          const [sectionResponse, itemsResponse] = await Promise.all([
            fetch(`${apiBaseUrl}/api/home-page/achievements/${locale}/`),
            fetch(`${apiBaseUrl}/api/home-page/achievements/items/${locale}/`)
          ]);

          return await Promise.all([
            sectionResponse.json(),
            itemsResponse.json()
          ]);
        };

        const [sectionData, itemsData] = await tryAdminEndpoints();

        console.log('📊 Section data received:', sectionData);
        console.log('📊 Items data received:', itemsData);

        if (sectionData.success) {
          // Handle both admin (bilingual) and public (single language) responses
          if (sectionData.data.badge && typeof sectionData.data.badge === 'object') {
            // Admin response - already bilingual
            setAchievementsSection(sectionData.data);
          } else {
            // Public response - convert to bilingual format
            setAchievementsSection({
              id: sectionData.data.id,
              badge: { en: sectionData.data.badge, ar: sectionData.data.badge },
              title: { en: sectionData.data.title, ar: sectionData.data.title },
              supporting_text: { en: sectionData.data.supporting_text, ar: sectionData.data.supporting_text },
              created_at: sectionData.data.created_at,
              updated_at: sectionData.data.updated_at
            });
          }
        }

        if (itemsData.success) {
          // Handle both admin (paginated) and public (direct array) responses
          let itemsArray: any[] = [];

          if (Array.isArray(itemsData.data)) {
            // Public endpoint - direct array
            itemsArray = itemsData.data;
          } else if (itemsData.data && itemsData.data.items && Array.isArray(itemsData.data.items)) {
            // Admin endpoint - paginated response with items array
            itemsArray = itemsData.data.items;
          } else {
            console.warn('Unexpected items data structure:', itemsData.data);
            itemsArray = [];
          }

          const items = itemsArray.map((item: any) => {
            if (item.title && typeof item.title === 'object') {
              // Admin response - already bilingual
              return item;
            } else {
              // Public response - convert to bilingual format
              return {
                ...item,
                title: { en: item.title, ar: item.title }
              };
            }
          });
          setAchievementItems(items);
        }
      } catch (error) {
        console.error('Error fetching achievements data:', error);
        alert('Error loading achievements data. Please check your connection and try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [locale, isAuthenticated, tokens]);

  // Save section data
  const handleSaveSection = async () => {
    if (!achievementsSection) return;

    if (!isAuthenticated || !tokens?.access) {
      alert('You must be logged in to save changes');
      return;
    }

    try {
      setIsSaving(true);

      // Use authenticatedApiCall for proper authentication handling
      const response = await authenticatedApiCall('/api/admin/home-page/achievements/', {
        method: 'PUT',
        body: JSON.stringify({
          badge: achievementsSection.badge,
          title: achievementsSection.title,
          supporting_text: achievementsSection.supporting_text
        })
      });

      if (response.success) {
        alert('Section saved successfully!');
        setEditingSection(false);
        // Update local state with response data
        if (response.data && typeof response.data === 'object') {
          const updatedData = response.data as AchievementsSection;
          setAchievementsSection(prev => prev ? {
            ...prev,
            ...updatedData,
            updated_at: updatedData.updated_at
          } : null);
        }
      } else {
        throw new Error(response.message || 'Failed to save section');
      }
    } catch (error) {
      console.error('Error saving section:', error);
      alert(`Error saving section: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsSaving(false);
    }
  };

  // Add new achievement item
  const addAchievementItem = () => {
    const newItem: AchievementItem = {
      id: Date.now(), // Temporary ID
      icon_name: "target",
      achievement_value: "0",
      title: {
        en: "New Achievement",
        ar: "إنجاز جديد"
      }
    };
    setAchievementItems(prev => [...prev, newItem]);
    setEditingItem(newItem.id);
  };

  // Save achievement item
  const handleSaveItem = async (item: AchievementItem) => {
    if (!isAuthenticated || !tokens?.access) {
      alert('You must be logged in to save changes');
      return;
    }

    try {
      setIsSaving(true);

      // Determine if this is a new item (temporary ID) or existing item
      const isNewItem = item.id > 1000000000; // Temporary IDs are large numbers

      if (isNewItem) {
        // Create new achievement item
        const response = await authenticatedApiCall('/api/admin/home-page/achievements/items/', {
          method: 'POST',
          body: JSON.stringify({
            icon_name: item.icon_name,
            achievement_value: item.achievement_value,
            title: item.title
          })
        });

        if (response.success && response.data) {
          // Replace temporary item with real item from API
          const newItem = response.data as AchievementItem;
          setAchievementItems(prev =>
            prev.map(i => i.id === item.id ? newItem : i)
          );
          alert('Achievement created successfully!');
        } else {
          throw new Error(response.message || 'Failed to create achievement');
        }
      } else {
        // Update existing achievement item
        const response = await authenticatedApiCall(`/api/admin/home-page/achievements/items/${item.id}/`, {
          method: 'PUT',
          body: JSON.stringify({
            icon_name: item.icon_name,
            achievement_value: item.achievement_value,
            title: item.title
          })
        });

        if (response.success && response.data) {
          // Update item in local state
          const updatedItem = response.data as AchievementItem;
          setAchievementItems(prev =>
            prev.map(i => i.id === item.id ? updatedItem : i)
          );
          alert('Achievement updated successfully!');
        } else {
          throw new Error(response.message || 'Failed to update achievement');
        }
      }

      setEditingItem(null);
    } catch (error) {
      console.error('Error saving item:', error);
      alert(`Error saving achievement: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsSaving(false);
    }
  };

  // Delete achievement item
  const deleteAchievementItem = async (id: number) => {
    if (!confirm('Are you sure you want to delete this achievement?')) return;

    // Check if this is a temporary item (not yet saved to API)
    const isTemporaryItem = id > 1000000000;

    if (isTemporaryItem) {
      // Just remove from local state since it's not saved to API yet
      setAchievementItems(prev => prev.filter(item => item.id !== id));
      alert('Achievement removed successfully!');
      return;
    }

    if (!isAuthenticated || !tokens?.access) {
      alert('You must be logged in to delete items');
      return;
    }

    try {
      setIsSaving(true);

      // Call admin API to delete the item
      const response = await authenticatedApiCall(`/api/admin/home-page/achievements/items/${id}/`, {
        method: 'DELETE'
      });

      if (response.success) {
        // Remove from local state
        setAchievementItems(prev => prev.filter(item => item.id !== id));
        alert('Achievement deleted successfully!');
      } else {
        throw new Error(response.message || 'Failed to delete achievement');
      }
    } catch (error) {
      console.error('Error deleting item:', error);
      alert(`Error deleting achievement: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <FiLoader className="h-8 w-8 animate-spin text-[#00C2FF]" />
        <span className="ml-2 text-white">Loading achievements data...</span>
      </div>
    );
  }

  return (
    <div>
      <div className="pb-5 border-b border-gray-700 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold leading-tight text-white">Home Page - Achievements Section</h1>
          <p className="text-gray-400 mt-1">Manage the achievements section content and statistics</p>
        </div>
        <button
          onClick={handleSaveSection}
          disabled={isSaving}
          className="inline-flex items-center px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors disabled:opacity-50"
        >
          {isSaving ? <FiLoader className="mr-2 h-4 w-4 animate-spin" /> : <FiSave className="mr-2 h-4 w-4" />}
          Save All Changes
        </button>
      </div>

      <div className="mt-6 space-y-8">
        {/* Section Content */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiAward className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Section Content
            </h2>
            <button
              onClick={() => setEditingSection(!editingSection)}
              className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
            >
              {editingSection ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
              {editingSection ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {editingSection && achievementsSection ? (
            <div className="space-y-6">
              {/* Badge */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Badge (English)</label>
                  <input
                    type="text"
                    value={achievementsSection.badge.en}
                    onChange={(e) => setAchievementsSection(prev => prev ? ({
                      ...prev,
                      badge: { ...prev.badge, en: e.target.value }
                    }) : null)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Badge (Arabic)</label>
                  <input
                    type="text"
                    value={achievementsSection.badge.ar}
                    onChange={(e) => setAchievementsSection(prev => prev ? ({
                      ...prev,
                      badge: { ...prev.badge, ar: e.target.value }
                    }) : null)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
              </div>

              {/* Title */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Title (English)</label>
                  <input
                    type="text"
                    value={achievementsSection.title.en}
                    onChange={(e) => setAchievementsSection(prev => prev ? ({
                      ...prev,
                      title: { ...prev.title, en: e.target.value }
                    }) : null)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
                  <input
                    type="text"
                    value={achievementsSection.title.ar}
                    onChange={(e) => setAchievementsSection(prev => prev ? ({
                      ...prev,
                      title: { ...prev.title, ar: e.target.value }
                    }) : null)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
              </div>

              {/* Supporting Text */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Supporting Text (English)</label>
                  <textarea
                    value={achievementsSection.supporting_text.en}
                    onChange={(e) => setAchievementsSection(prev => prev ? ({
                      ...prev,
                      supporting_text: { ...prev.supporting_text, en: e.target.value }
                    }) : null)}
                    rows={4}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Supporting Text (Arabic)</label>
                  <textarea
                    value={achievementsSection.supporting_text.ar}
                    onChange={(e) => setAchievementsSection(prev => prev ? ({
                      ...prev,
                      supporting_text: { ...prev.supporting_text, ar: e.target.value }
                    }) : null)}
                    rows={4}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
              </div>
            </div>
          ) : (
            achievementsSection && (
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-3">Current Badge</label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <span className="text-xs text-gray-500 block mb-1">English</span>
                      <p className="text-white bg-gray-700 p-3 rounded">{achievementsSection.badge.en}</p>
                    </div>
                    <div>
                      <span className="text-xs text-gray-500 block mb-1">Arabic</span>
                      <p className="text-white bg-gray-700 p-3 rounded text-right" dir="rtl">{achievementsSection.badge.ar}</p>
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-3">Current Title</label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <span className="text-xs text-gray-500 block mb-1">English</span>
                      <p className="text-white bg-gray-700 p-3 rounded">{achievementsSection.title.en}</p>
                    </div>
                    <div>
                      <span className="text-xs text-gray-500 block mb-1">Arabic</span>
                      <p className="text-white bg-gray-700 p-3 rounded text-right" dir="rtl">{achievementsSection.title.ar}</p>
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-3">Current Supporting Text</label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <span className="text-xs text-gray-500 block mb-1">English</span>
                      <p className="text-gray-300 bg-gray-700 p-3 rounded text-sm">{achievementsSection.supporting_text.en}</p>
                    </div>
                    <div>
                      <span className="text-xs text-gray-500 block mb-1">Arabic</span>
                      <p className="text-gray-300 bg-gray-700 p-3 rounded text-sm text-right" dir="rtl">{achievementsSection.supporting_text.ar}</p>
                    </div>
                  </div>
                </div>
              </div>
            )
          )}
        </div>

        {/* Achievement Items Section */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiAward className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Achievement Items ({achievementItems.length})
            </h2>
            <button
              onClick={addAchievementItem}
              disabled={isSaving}
              className="inline-flex items-center px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors disabled:opacity-50"
            >
              <FiPlus className="mr-2 h-4 w-4" />
              Add Achievement
            </button>
          </div>

          <div className="space-y-6">
            {achievementItems.map((item, index) => (
              <div key={item.id} className="bg-gray-700 rounded-lg border border-gray-600 p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-white flex items-center">
                    Achievement #{index + 1}
                  </h3>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setEditingItem(editingItem === item.id ? null : item.id)}
                      className="inline-flex items-center px-3 py-1 text-sm bg-gray-600 text-gray-300 rounded hover:bg-gray-500"
                    >
                      {editingItem === item.id ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
                      {editingItem === item.id ? 'Cancel' : 'Edit'}
                    </button>
                    <button
                      onClick={() => deleteAchievementItem(item.id)}
                      className="inline-flex items-center px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-500"
                    >
                      <FiTrash2 className="mr-1 h-4 w-4" />
                      Delete
                    </button>
                  </div>
                </div>

                {editingItem === item.id ? (
                  <AchievementForm
                    achievement={item}
                    onSave={handleSaveItem}
                    onCancel={() => setEditingItem(null)}
                  />
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-1">Icon</label>
                      <span className="text-gray-300 text-sm capitalize">{item.icon_name}</span>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-1">Value</label>
                      <p className="text-white font-bold text-lg">{item.achievement_value}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-1">Title (English)</label>
                      <p className="text-gray-300 text-sm">{item.title.en}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-1">Title (Arabic)</label>
                      <p className="text-gray-300 text-sm text-right" dir="rtl">{item.title.ar}</p>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Preview Section */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <h2 className="text-xl font-semibold text-white flex items-center mb-4">
            <FiEye className="mr-2 h-5 w-5 text-[#00C2FF]" />
            Live Preview
          </h2>
          <div className="bg-gray-900 rounded-lg p-8 border border-gray-600">
            {achievementsSection && (
              <div className="text-center mb-16">
                <div className="inline-block px-4 py-1.5 rounded-full bg-white/10 backdrop-blur-md border border-white/20 mb-6">
                  <span className="text-sm font-medium tracking-wide text-white">{achievementsSection.badge.en}</span>
                </div>
                <h2 className="text-4xl md:text-5xl font-bold mb-6 leading-tight text-white">
                  {achievementsSection.title.en}
                </h2>
                <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                  {achievementsSection.supporting_text.en}
                </p>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
              {achievementItems.map((item) => (
                <div key={item.id} className="group">
                  <div className="relative h-full bg-white/10 backdrop-blur-md border border-white/20 rounded-xl p-8 overflow-hidden transition-all duration-500 hover:bg-white/15 hover:border-white/30">
                    <div className="relative z-10">
                      <div className="flex justify-center mb-6 text-[#00C2FF]">
                        <div className="h-8 w-8 bg-[#00C2FF] rounded"></div>
                      </div>
                      <div className="text-5xl font-bold mb-3 text-white text-center">
                        {item.achievement_value}
                      </div>
                      <div className="text-white font-medium text-center">
                        {item.title.en}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
