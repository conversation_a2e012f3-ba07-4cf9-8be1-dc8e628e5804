"use client";

import React, { useState, useRef, useEffect } from 'react';
import { FiSearch, FiChevronLeft, FiChevronRight } from 'react-icons/fi';
import * as FaIcons from 'react-icons/fa';
import * as FaIconsSolid from 'react-icons/fa6';
import * as BsIcons from 'react-icons/bs';
import * as RiIcons from 'react-icons/ri';
import * as GiIcons from 'react-icons/gi';
import * as TbIcons from 'react-icons/tb';
import * as MdIcons from 'react-icons/md';
import * as HiIcons from 'react-icons/hi';
import * as AiIcons from 'react-icons/ai';
import * as IoIcons from 'react-icons/io';
import * as Io5Icons from 'react-icons/io5';
import * as PiIcons from 'react-icons/pi';
import * as FcIcons from 'react-icons/fc';

interface IconPickerProps {
  value: string;
  onChange: (iconName: string) => void;
  className?: string;
  placeholder?: string;
}

const iconLibraries = [
  { id: 'fc', name: 'Flat Color', library: FcIcons },
  { id: 'fa', name: 'Font Awesome', library: FaIcons },
  { id: 'fa6', name: 'Font Awesome 6', library: FaIconsSolid },
  { id: 'bs', name: 'Bootstrap', library: BsIcons },
  { id: 'ri', name: 'Remix', library: RiIcons },
  { id: 'gi', name: 'Game Icons', library: GiIcons },
  { id: 'tb', name: 'Tabler', library: TbIcons },
  { id: 'md', name: 'Material Design', library: MdIcons },
  { id: 'hi', name: 'Heroicons', library: HiIcons },
  { id: 'ai', name: 'Ant Design', library: AiIcons },
  { id: 'io', name: 'Ionicons 4', library: IoIcons },
  { id: 'io5', name: 'Ionicons 5', library: Io5Icons },
  { id: 'pi', name: 'Phosphor', library: PiIcons },
];

// Function to get the icon component based on the icon name
const getIconComponent = (iconName: string): React.ReactElement | null => {
  if (!iconName) return null;
  
  // Determine which library the icon belongs to based on prefix
  if (iconName.startsWith('Fc')) {
      const IconFc = FcIcons[iconName as keyof typeof FcIcons];
      return IconFc ? <IconFc className="text-lg" /> : null;
  } else if (iconName.startsWith('Fa6')) {
      const IconFa6 = FaIconsSolid[iconName as keyof typeof FaIconsSolid];
      return IconFa6 ? <IconFa6 className="text-lg" /> : null;
  } else if (iconName.startsWith('Fa')) {
    const IconFa = FaIcons[iconName as keyof typeof FaIcons];
    return IconFa ? <IconFa className="text-lg" /> : null;
  } else if (iconName.startsWith('Bs')) {
      const IconBs = BsIcons[iconName as keyof typeof BsIcons];
      return IconBs ? <IconBs className="text-lg" /> : null;
  } else if (iconName.startsWith('Ri')) {
      const IconRi = RiIcons[iconName as keyof typeof RiIcons];
      return IconRi ? <IconRi className="text-lg" /> : null;
  } else if (iconName.startsWith('Gi')) {
      const IconGi = GiIcons[iconName as keyof typeof GiIcons];
      return IconGi ? <IconGi className="text-lg" /> : null;
  } else if (iconName.startsWith('Tb')) {
      const IconTb = TbIcons[iconName as keyof typeof TbIcons];
      return IconTb ? <IconTb className="text-lg" /> : null;
  } else if (iconName.startsWith('Md')) {
      const IconMd = MdIcons[iconName as keyof typeof MdIcons];
      return IconMd ? <IconMd className="text-lg" /> : null;
  } else if (iconName.startsWith('Hi')) {
      const IconHi = HiIcons[iconName as keyof typeof HiIcons];
      return IconHi ? <IconHi className="text-lg" /> : null;
  } else if (iconName.startsWith('Ai')) {
      const IconAi = AiIcons[iconName as keyof typeof AiIcons];
      return IconAi ? <IconAi className="text-lg" /> : null;
  } else if (iconName.startsWith('Io5')) {
      const IconIo5 = Io5Icons[iconName as keyof typeof Io5Icons];
      return IconIo5 ? <IconIo5 className="text-lg" /> : null;
  } else if (iconName.startsWith('Io')) {
    const IconIo = IoIcons[iconName as keyof typeof IoIcons];
    return IconIo ? <IconIo className="text-lg" /> : null;
  } else if (iconName.startsWith('Pi')) {
      const IconPi = PiIcons[iconName as keyof typeof PiIcons];
      return IconPi ? <IconPi className="text-lg" /> : null;
    }
  
      return null;
};

// Function to determine if a library has any icons matching the search term
const hasMatchingIcons = (libraryId: string, searchTerm: string) => {
  if (!searchTerm.trim()) return true;
  
  let library;
  switch (libraryId) {
    case 'fc': library = FcIcons; break;
    case 'fa': library = FaIcons; break;
    case 'fa6': library = FaIconsSolid; break;
    case 'bs': library = BsIcons; break;
    case 'ri': library = RiIcons; break;
    case 'gi': library = GiIcons; break;
    case 'tb': library = TbIcons; break;
    case 'md': library = MdIcons; break;
    case 'hi': library = HiIcons; break;
    case 'ai': library = AiIcons; break;
    case 'io': library = IoIcons; break;
    case 'io5': library = Io5Icons; break;
    case 'pi': library = PiIcons; break;
    default: return false;
  }
  
  const normalizedSearch = searchTerm.trim().toLowerCase();
  return Object.keys(library).some(iconName => 
    iconName.toLowerCase().includes(normalizedSearch)
  );
};

// Function to get filtered icons from a library
const getFilteredIcons = (library: any, searchTerm: string): string[] => {
  if (!library) return [];
  
  const normalizedSearch = searchTerm.trim().toLowerCase();
  const iconNames = Object.keys(library);
  
  if (!normalizedSearch) {
    return iconNames.slice(0, 50); // Limit to first 50 icons when no search
  }
  
  return iconNames
    .filter(iconName => iconName.toLowerCase().includes(normalizedSearch))
    .slice(0, 50); // Limit to 50 results
};

export default function IconPicker({ value, onChange, className = "", placeholder = "Select Icon" }: IconPickerProps) {
  const [showIconSelector, setShowIconSelector] = useState(false);
  const [iconSearchTerm, setIconSearchTerm] = useState('');
  const [selectedIconSet, setSelectedIconSet] = useState<
    'fc' | 'fa' | 'fa6' | 'bs' | 'ri' | 'gi' | 'tb' | 'md' | 'hi' | 'ai' | 'io' | 'io5' | 'pi'
  >('fa');
  
  // Icon library carousel refs and state
  const iconLibsCarouselRef = useRef<HTMLDivElement>(null);
  const [canScrollLibsLeft, setCanScrollLibsLeft] = useState(false);
  const [canScrollLibsRight, setCanScrollLibsRight] = useState(true);

  // Icon library carousel scroll functions
  const scrollLibsLeft = () => {
    if (iconLibsCarouselRef.current) {
      iconLibsCarouselRef.current.scrollBy({
        left: -120,
        behavior: 'smooth'
      });
    }
  };

  const scrollLibsRight = () => {
    if (iconLibsCarouselRef.current) {
      iconLibsCarouselRef.current.scrollBy({
        left: 120,
        behavior: 'smooth'
      });
    }
  };

  // Check if scrolling is possible for icon libraries
  const checkLibsScrollPosition = () => {
    if (iconLibsCarouselRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = iconLibsCarouselRef.current;
      setCanScrollLibsLeft(scrollLeft > 0);
      setCanScrollLibsRight(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  useEffect(() => {
    const carousel = iconLibsCarouselRef.current;
    if (carousel) {
      carousel.addEventListener('scroll', checkLibsScrollPosition);
      checkLibsScrollPosition(); // Initial check
      
      return () => {
        carousel.removeEventListener('scroll', checkLibsScrollPosition);
      };
    }
  }, []);

  // Close icon selector when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showIconSelector && !(event.target as Element).closest('.icon-selector-container')) {
        setShowIconSelector(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showIconSelector]);

  return (
    <div className={`relative icon-selector-container ${className}`}>
      <button
        type="button"
        onClick={() => setShowIconSelector(!showIconSelector)}
        className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] flex items-center justify-between"
      >
        <span className="flex items-center min-w-0 flex-1">
          <span className="flex-shrink-0">
            {getIconComponent(value) || <FaIcons.FaBuilding className="text-lg" />}
          </span>
          <span className="ml-2 text-sm truncate min-w-0">{value || placeholder}</span>
        </span>
        <FiSearch className="h-4 w-4" />
      </button>

      {showIconSelector && (
        <div className="absolute z-50 w-80 mt-1 bg-gray-700 border border-gray-600 rounded-lg shadow-lg">
          {/* Library Tabs with Navigation */}
          <div className="relative">
            {/* Left Arrow */}
            {canScrollLibsLeft && (
              <button
                type="button"
                onClick={scrollLibsLeft}
                className="absolute left-0 top-0 z-10 h-full w-6 bg-gradient-to-r from-gray-700 to-transparent flex items-center justify-start pl-1"
              >
                <FiChevronLeft className="h-3 w-3 text-white" />
              </button>
            )}

            {/* Right Arrow */}
            {canScrollLibsRight && (
              <button
                type="button"
                onClick={scrollLibsRight}
                className="absolute right-0 top-0 z-10 h-full w-6 bg-gradient-to-l from-gray-700 to-transparent flex items-center justify-end pr-1"
              >
                <FiChevronRight className="h-3 w-3 text-white" />
              </button>
            )}

            {/* Scrollable Tabs Container */}
            <div 
              ref={iconLibsCarouselRef}
              className="flex gap-1 p-2 overflow-x-auto scrollbar-hide px-6"
              style={{
                scrollbarWidth: 'none',
                msOverflowStyle: 'none',
              }}
            >
              {iconLibraries.filter(lib => hasMatchingIcons(lib.id, iconSearchTerm)).map((lib) => (
                <button
                  key={lib.id}
                  type="button"
                  onClick={() => setSelectedIconSet(lib.id as any)}
                  className={`px-2 py-1 text-xs rounded transition-colors whitespace-nowrap flex-shrink-0 ${
                    selectedIconSet === lib.id
                      ? 'bg-[#00C2FF] text-white'
                      : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
                  }`}
                >
                  {lib.name}
                </button>
              ))}
            </div>
          </div>

          {/* Search Input */}
          <div className="p-2 border-b border-gray-600">
            <input
              type="text"
              placeholder="Search icons..."
              value={iconSearchTerm}
              onChange={(e) => setIconSearchTerm(e.target.value)}
              className="w-full px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm"
            />
          </div>

          {/* Icon Grid */}
          <div className="p-2 max-h-64 overflow-y-auto">
            <div className="grid grid-cols-6 gap-1">
              {(() => {
                const currentLibrary = iconLibraries.find(lib => lib.id === selectedIconSet)?.library;
                if (!currentLibrary) return null;

                return getFilteredIcons(currentLibrary, iconSearchTerm).map((iconName) => {
                  const IconComponent = currentLibrary[iconName as keyof typeof currentLibrary];
                  
                  return (
                    <button
                      key={iconName}
                      type="button"
                      onClick={() => {
                        onChange(iconName);
                        setShowIconSelector(false);
                      }}
                      className="p-2 bg-gray-600 hover:bg-gray-500 rounded flex items-center justify-center transition-colors"
                      title={iconName}
                    >
                      {IconComponent && <IconComponent className="text-lg text-white hover:text-[#00C2FF] transition-colors" />}
                    </button>
                  );
                });
              })()}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// Export the getIconComponent function for use in other components
export { getIconComponent };
