@tailwind base;
@tailwind components;
@tailwind utilities;

/* Light theme */
:root {
  /* Mazaya Capital Brand Colors - Light Mode */
  --color-primary: 14, 198, 224; /* #0EC6E0 - Light Blue */
  --color-primary-hover: 0, 157, 181; /* #009DB5 - Darker Blue */
  --color-secondary: 151, 71, 255; /* #9747FF - Purple */
  --color-background: 255, 255, 255; /* #FFFFFF - White */
  --color-text: 10, 15, 35; /* #0A0F23 - Dark Navy */
  --color-text-secondary: 75, 85, 99; /* #4B5563 - Gray */
}

/* Dark theme */
.dark {
  --color-primary: 14, 198, 224; /* #0EC6E0 - Light Blue */
  --color-primary-hover: 0, 157, 181; /* #009DB5 - Darker Blue */
  --color-secondary: 151, 71, 255; /* #9747FF - Purple */
  --color-background: 10, 15, 35; /* #0A0F23 - Dark Navy */
  --color-text: 255, 255, 255; /* #FFFFFF - White */
  --color-text-secondary: 208, 213, 221; /* #D0D5DD - <PERSON> Gray */
}

html {
  scroll-behavior: smooth;
}

body {
  color: rgb(var(--color-text));
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: rgb(var(--color-background));
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 500;
}

.container {
  max-width: 80rem;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

.page-heading {
  font-size: 2.25rem;
  line-height: 2.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
}

@media (min-width: 768px) {
  .page-heading {
    font-size: 3rem;
    line-height: 1;
  }
}

.section-heading {
  font-size: 1.875rem;
  line-height: 2.25rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

@media (min-width: 768px) {
  .section-heading {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
}

.text-balance {
  text-wrap: balance;
}

/* Gradient text class for brand elements */
.brand-gradient-text {
  background: linear-gradient(90deg, #0EC6E0 0%, #9747FF 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Gradient background class */
.brand-gradient-bg {
  background: linear-gradient(90deg, #0EC6E0 0%, #9747FF 100%);
}

/* Add transition to theme changes */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-duration: 200ms;
  transition-timing-function: ease-in-out;
}

/* Glow Effect for Project Cards - Disabled */
.glow-card {
  position: relative;
  transition: all 0.3s ease;
}

.glow-card::before {
  content: "";
  position: absolute;
  inset: -2px;
  background: transparent;
  z-index: -1;
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.glow-card::after {
  content: "";
  position: absolute;
  inset: -2px;
  background: transparent;
  z-index: -2;
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.glow-card:hover {
  transform: translateY(-3px);
  box-shadow: none;
}

.glow-card:hover::before,
.glow-card:hover::after {
  opacity: 0;
}

/* Stronger glow variant - Disabled */
.glow-card-strong::before {
  filter: none;
  inset: -4px;
}

.glow-card-strong::after {
  filter: none;
  inset: -4px;
}

.glow-card-strong:hover {
  box-shadow: none;
}

/* Pulse animation for the glow - Disabled */
@keyframes glow-pulse {
  0% { opacity: 0; }
  50% { opacity: 0; }
  100% { opacity: 0; }
}

.glow-card-pulse::before,
.glow-card-pulse::after {
  animation: none;
}

/* Pulse slow animation */
@keyframes pulse-slow {
  0%, 100% { 
    opacity: 0.7;
    transform: scale(1);
  }
  50% { 
    opacity: 0.9;
    transform: scale(1.05);
  }
}

.animate-pulse-slow {
  animation: pulse-slow 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Add the parallax animations at the end of the file */

/* Parallax Animation Styles */
@keyframes parallax-slow {
  0% { transform: translateY(30%); }
  100% { transform: translateY(-70%); }
}

@keyframes parallax-medium {
  0% { transform: translateY(20%); }
  100% { transform: translateY(-80%); }
}

@keyframes parallax-fast {
  0% { transform: translateY(40%); }
  100% { transform: translateY(-60%); }
}

.parallax-slow {
  animation: parallax-slow 20s linear infinite alternate;
}

.parallax-medium {
  animation: parallax-medium 15s linear infinite alternate;
}

.parallax-fast {
  animation: parallax-fast 10s linear infinite alternate;
}

/* Twinkle animation for stars */
@keyframes twinkle {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

/* Star classes for consistent usage */
.star {
  position: absolute;
  background-color: white;
  border-radius: 50%;
}

.star-field {
  position: absolute;
  inset: 0;
  z-index: 5;
  opacity: 0.7;
  pointer-events: none;
}

/* Custom Scrollbar Styles for Admin Sidebar */
.admin-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgb(75, 85, 99) rgb(31, 41, 55);
}

.admin-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.admin-scrollbar::-webkit-scrollbar-track {
  background: rgb(31, 41, 55);
  border-radius: 10px;
}

.admin-scrollbar::-webkit-scrollbar-thumb {
  background: rgb(75, 85, 99);
  border-radius: 10px;
  transition: background-color 0.2s ease;
}

.admin-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgb(107, 114, 128);
}

/* Global Thin Gray Scrollbar Styles */
* {
  scrollbar-width: thin;
  scrollbar-color: #9ca3af transparent;
}

*::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

*::-webkit-scrollbar-track {
  background: transparent;
}

*::-webkit-scrollbar-thumb {
  background: #9ca3af;
  border-radius: 3px;
  border: none;
}

*::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

*::-webkit-scrollbar-corner {
  background: transparent;
}

/* Tiptap Rich Text Editor Styles */
.prose-editor {
  background: white;
  color: #333;
}

.prose-editor .ProseMirror {
  outline: none;
  padding: 1rem;
  min-height: 300px;
  max-height: 500px;
  overflow-y: auto;
}

.prose-editor .ProseMirror p {
  margin: 0.5rem 0;
}

.prose-editor .ProseMirror h1 {
  font-size: 2rem;
  font-weight: bold;
  margin: 1rem 0;
  color: #1f2937;
}

.prose-editor .ProseMirror h2 {
  font-size: 1.5rem;
  font-weight: bold;
  margin: 1rem 0;
  color: #1f2937;
}

.prose-editor .ProseMirror h3 {
  font-size: 1.25rem;
  font-weight: bold;
  margin: 1rem 0;
  color: #1f2937;
}

.prose-editor .ProseMirror ul,
.prose-editor .ProseMirror ol {
  margin: 1rem 0;
  padding-left: 2rem;
}

.prose-editor .ProseMirror li {
  margin: 0.25rem 0;
}

.prose-editor .ProseMirror blockquote {
  border-left: 4px solid #00C2FF;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #6b7280;
}

.prose-editor .ProseMirror code {
  background: #f3f4f6;
  padding: 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', monospace;
  color: #dc2626;
}

.prose-editor .ProseMirror pre {
  background: #1f2937;
  color: white;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1rem 0;
}

.prose-editor .ProseMirror pre code {
  background: transparent;
  color: white;
  padding: 0;
}

.prose-editor .ProseMirror a {
  color: #00C2FF;
  text-decoration: underline;
}

.prose-editor .ProseMirror a:hover {
  color: #009DB5;
}

.prose-editor .ProseMirror table {
  border-collapse: collapse;
  margin: 1rem 0;
  width: 100%;
}

.prose-editor .ProseMirror th,
.prose-editor .ProseMirror td {
  border: 1px solid #d1d5db;
  padding: 0.5rem;
  text-align: left;
}

.prose-editor .ProseMirror th {
  background: #f9fafb;
  font-weight: bold;
}

.prose-editor .ProseMirror mark {
  background: #fef08a;
  padding: 0.125rem;
  border-radius: 0.125rem;
}

.prose-editor .ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 1rem 0;
}

/* RTL Support */
.prose-editor .ProseMirror[dir="rtl"] {
  text-align: right;
}

.prose-editor .ProseMirror[dir="rtl"] ul,
.prose-editor .ProseMirror[dir="rtl"] ol {
  padding-right: 2rem;
  padding-left: 0;
}

.prose-editor .ProseMirror[dir="rtl"] blockquote {
  border-left: none;
  border-right: 4px solid #00C2FF;
  padding-left: 0;
  padding-right: 1rem;
}

.prose-editor .ProseMirror[dir="rtl"] th,
.prose-editor .ProseMirror[dir="rtl"] td {
  text-align: right;
}

/* Task Lists */
.prose-editor .ProseMirror ul[data-type="taskList"] {
  list-style: none;
  padding-left: 0;
}

.prose-editor .ProseMirror ul[data-type="taskList"] li {
  display: flex;
  align-items: flex-start;
  margin: 0.5rem 0;
}

.prose-editor .ProseMirror ul[data-type="taskList"] li > label {
  flex: 0 0 auto;
  margin-right: 0.5rem;
  user-select: none;
}

.prose-editor .ProseMirror ul[data-type="taskList"] li > div {
  flex: 1 1 auto;
}

.prose-editor .ProseMirror ul[data-type="taskList"] input[type="checkbox"] {
  cursor: pointer;
}

/* Enhanced Table Styles */
.prose-editor .ProseMirror .tableWrapper {
  overflow-x: auto;
  margin: 1rem 0;
}

.prose-editor .ProseMirror table {
  border-collapse: collapse;
  table-layout: fixed;
  width: 100%;
  margin: 0;
  overflow: hidden;
}

.prose-editor .ProseMirror table td,
.prose-editor .ProseMirror table th {
  min-width: 1em;
  border: 2px solid #ced4da;
  padding: 3px 5px;
  vertical-align: top;
  box-sizing: border-box;
  position: relative;
}

.prose-editor .ProseMirror table th {
  font-weight: bold;
  text-align: left;
  background-color: #f1f3f4;
}

.prose-editor .ProseMirror .selectedCell:after {
  z-index: 2;
  position: absolute;
  content: "";
  left: 0; right: 0; top: 0; bottom: 0;
  background: rgba(200, 200, 255, 0.4);
  pointer-events: none;
}

.prose-editor .ProseMirror .column-resize-handle {
  position: absolute;
  right: -2px;
  top: 0;
  bottom: -2px;
  width: 4px;
  background-color: #adf;
  pointer-events: none;
}

.prose-editor .ProseMirror table p {
  margin: 0;
}

/* Horizontal Rule */
.prose-editor .ProseMirror hr {
  border: none;
  height: 2px;
  background: linear-gradient(to right, transparent, #00C2FF, transparent);
  margin: 2rem 0;
}

/* Subscript and Superscript */
.prose-editor .ProseMirror sub {
  vertical-align: sub;
  font-size: smaller;
}

.prose-editor .ProseMirror sup {
  vertical-align: super;
  font-size: smaller;
}

/* Enhanced Focus and Selection */
.prose-editor .ProseMirror-focused {
  outline: none;
}

.prose-editor .ProseMirror ::selection {
  background: rgba(0, 194, 255, 0.3);
}

/* Image resizing */
.prose-editor .ProseMirror img.ProseMirror-selectednode {
  outline: 3px solid #00C2FF;
}

/* Code improvements */
.prose-editor .ProseMirror .hljs {
  color: #ffffff;
  background: #1f2937;
}

/* Link improvements */
.prose-editor .ProseMirror a.ProseMirror-selectednode {
  outline: 2px solid #00C2FF;
  background-color: rgba(0, 194, 255, 0.1);
}

/* Dark Theme Tiptap Rich Text Editor Styles */
.prose-editor-dark {
  background: #1f2937;
  color: #f3f4f6;
}

.prose-editor-dark .ProseMirror {
  outline: none;
  padding: 1rem;
  min-height: 300px;
  max-height: 500px;
  overflow-y: auto;
  background: #1f2937;
  color: #f3f4f6;
}

.prose-editor-dark .ProseMirror p {
  margin: 0.5rem 0;
  color: #f3f4f6;
}

.prose-editor-dark .ProseMirror h1 {
  font-size: 2rem;
  font-weight: bold;
  margin: 1rem 0;
  color: #ffffff;
}

.prose-editor-dark .ProseMirror h2 {
  font-size: 1.5rem;
  font-weight: bold;
  margin: 1rem 0;
  color: #ffffff;
}

.prose-editor-dark .ProseMirror h3 {
  font-size: 1.25rem;
  font-weight: bold;
  margin: 1rem 0;
  color: #ffffff;
}

.prose-editor-dark .ProseMirror h4,
.prose-editor-dark .ProseMirror h5,
.prose-editor-dark .ProseMirror h6 {
  font-weight: bold;
  margin: 1rem 0;
  color: #ffffff;
}

.prose-editor-dark .ProseMirror ul,
.prose-editor-dark .ProseMirror ol {
  margin: 1rem 0;
  padding-left: 2rem;
}

.prose-editor-dark .ProseMirror li {
  margin: 0.25rem 0;
  color: #f3f4f6;
}

.prose-editor-dark .ProseMirror blockquote {
  border-left: 4px solid #00C2FF;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #d1d5db;
  background: rgba(55, 65, 81, 0.5);
  border-radius: 0.25rem;
}

.prose-editor-dark .ProseMirror code {
  background: #374151;
  padding: 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', monospace;
  color: #fbbf24;
}

.prose-editor-dark .ProseMirror pre {
  background: #111827;
  color: #f3f4f6;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1rem 0;
  border: 1px solid #374151;
}

.prose-editor-dark .ProseMirror pre code {
  background: transparent;
  color: #f3f4f6;
  padding: 0;
}

.prose-editor-dark .ProseMirror a {
  color: #60a5fa;
  text-decoration: underline;
}

.prose-editor-dark .ProseMirror a:hover {
  color: #93c5fd;
}

.prose-editor-dark .ProseMirror table {
  border-collapse: collapse;
  margin: 1rem 0;
  width: 100%;
  background: #374151;
  border-radius: 0.5rem;
  overflow: hidden;
}

.prose-editor-dark .ProseMirror th,
.prose-editor-dark .ProseMirror td {
  border: 1px solid #4b5563;
  padding: 0.75rem;
  text-align: left;
  color: #f3f4f6;
}

.prose-editor-dark .ProseMirror th {
  background: #4b5563;
  font-weight: bold;
  color: #ffffff;
}

.prose-editor-dark .ProseMirror mark {
  background: #fbbf24;
  color: #111827;
  padding: 0.125rem;
  border-radius: 0.125rem;
}

.prose-editor-dark .ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 1rem 0;
  border: 1px solid #4b5563;
}

/* Dark RTL Support */
.prose-editor-dark .ProseMirror[dir="rtl"] {
  text-align: right;
}

.prose-editor-dark .ProseMirror[dir="rtl"] ul,
.prose-editor-dark .ProseMirror[dir="rtl"] ol {
  padding-right: 2rem;
  padding-left: 0;
}

.prose-editor-dark .ProseMirror[dir="rtl"] blockquote {
  border-left: none;
  border-right: 4px solid #00C2FF;
  padding-left: 0;
  padding-right: 1rem;
}

.prose-editor-dark .ProseMirror[dir="rtl"] th,
.prose-editor-dark .ProseMirror[dir="rtl"] td {
  text-align: right;
}

/* Dark Task Lists */
.prose-editor-dark .ProseMirror ul[data-type="taskList"] {
  list-style: none;
  padding-left: 0;
}

.prose-editor-dark .ProseMirror ul[data-type="taskList"] li {
  display: flex;
  align-items: flex-start;
  margin: 0.5rem 0;
}

.prose-editor-dark .ProseMirror ul[data-type="taskList"] li > label {
  flex: 0 0 auto;
  margin-right: 0.5rem;
  user-select: none;
}

.prose-editor-dark .ProseMirror ul[data-type="taskList"] li > div {
  flex: 1 1 auto;
  color: #f3f4f6;
}

.prose-editor-dark .ProseMirror ul[data-type="taskList"] input[type="checkbox"] {
  cursor: pointer;
  accent-color: #00C2FF;
}

/* Dark Enhanced Table Styles */
.prose-editor-dark .ProseMirror .tableWrapper {
  overflow-x: auto;
  margin: 1rem 0;
}

.prose-editor-dark .ProseMirror table {
  border-collapse: collapse;
  table-layout: fixed;
  width: 100%;
  margin: 0;
  overflow: hidden;
}

.prose-editor-dark .ProseMirror table td,
.prose-editor-dark .ProseMirror table th {
  min-width: 1em;
  border: 2px solid #4b5563;
  padding: 3px 5px;
  vertical-align: top;
  box-sizing: border-box;
  position: relative;
  background: #374151;
}

.prose-editor-dark .ProseMirror table th {
  font-weight: bold;
  text-align: left;
  background-color: #4b5563;
  color: #ffffff;
}

.prose-editor-dark .ProseMirror .selectedCell:after {
  z-index: 2;
  position: absolute;
  content: "";
  left: 0; right: 0; top: 0; bottom: 0;
  background: rgba(59, 130, 246, 0.3);
  pointer-events: none;
}

.prose-editor-dark .ProseMirror .column-resize-handle {
  position: absolute;
  right: -2px;
  top: 0;
  bottom: -2px;
  width: 4px;
  background-color: #60a5fa;
  pointer-events: none;
}

.prose-editor-dark .ProseMirror table p {
  margin: 0;
  color: #f3f4f6;
}

/* Dark Horizontal Rule */
.prose-editor-dark .ProseMirror hr {
  border: none;
  height: 2px;
  background: linear-gradient(to right, transparent, #60a5fa, transparent);
  margin: 2rem 0;
}

/* Dark Subscript and Superscript */
.prose-editor-dark .ProseMirror sub {
  vertical-align: sub;
  font-size: smaller;
  color: #d1d5db;
}

.prose-editor-dark .ProseMirror sup {
  vertical-align: super;
  font-size: smaller;
  color: #d1d5db;
}

/* Dark Enhanced Focus and Selection */
.prose-editor-dark .ProseMirror-focused {
  outline: none;
}

.prose-editor-dark .ProseMirror ::selection {
  background: rgba(59, 130, 246, 0.3);
}

/* Dark Image resizing */
.prose-editor-dark .ProseMirror img.ProseMirror-selectednode {
  outline: 3px solid #60a5fa;
}

/* Dark Code improvements */
.prose-editor-dark .ProseMirror .hljs {
  color: #f3f4f6;
  background: #111827;
}

/* Dark Link improvements */
.prose-editor-dark .ProseMirror a.ProseMirror-selectednode {
  outline: 2px solid #60a5fa;
  background-color: rgba(59, 130, 246, 0.1);
}

/* Dark Strong and Emphasis */
.prose-editor-dark .ProseMirror strong {
  color: #ffffff;
  font-weight: bold;
}

.prose-editor-dark .ProseMirror em {
  color: #d1d5db;
  font-style: italic;
}

/* Dark Strikethrough */
.prose-editor-dark .ProseMirror s {
  color: #9ca3af;
  text-decoration: line-through;
}


